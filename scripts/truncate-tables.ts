#!/usr/bin/env node
/**
 * Script to truncate all tables in the database
 * This script will truncate all tables defined in the src/entities folder
 *
 * Usage: npm run truncate-tables
 */

import { MikroORM, PostgreSqlDriver } from '@mikro-orm/postgresql';
import 'dotenv/config';
import config from '../mikro-orm.config.js';
import { logger } from '../src/utils/logger.js';

// Define the order of tables to truncate based on foreign key dependencies
// Tables with foreign keys should be truncated before the tables they reference
const TRUNCATE_ORDER = [
  // Child tables first
  'player_match_history',
  'player_overall_stats',
  'player_attributes',
  'fixture',
  'scouted_players',
  'players',
  'transfer_list',
  'available_team',
  'team',
  'league_rules',
  'league_children',
  'league',
  'manager',
  'gameworld',
  'scouting_requests',
];

async function truncateTables() {
  logger.info('Starting table truncation process');

  // Initialize MikroORM
  logger.info('Connecting to database...');
  const orm = await MikroORM.init<PostgreSqlDriver>(config);
  const connection = orm.em.getConnection();

  try {
    // Disable foreign key constraints without a transaction
    logger.info('Disabling foreign key constraints...');
    await connection.execute('SET session_replication_role = replica;');

    // Truncate tables in the defined order
    for (const tableName of TRUNCATE_ORDER) {
      try {
        logger.info(`Truncating table: ${tableName}`);
        await connection.execute(`TRUNCATE TABLE "${tableName}" CASCADE`);
        logger.info(`Successfully truncated table: ${tableName}`);
      } catch (error) {
        logger.error(`Error truncating table ${tableName}:`, { error });
        throw error;
      }
    }

    logger.info('Re-enabling foreign key constraints...');
    await connection.execute('SET session_replication_role = default;');
    logger.info('All tables have been successfully truncated');
  } catch (error) {
    logger.error('Failed to truncate tables:', { error });
    // Make sure constraints are re-enabled even if there's an error
    try {
      await connection.execute('SET session_replication_role = default;');
    } catch (innerError) {
      logger.error('Error re-enabling constraints:', { innerError });
    }
    throw error;
  } finally {
    // Close the ORM connection
    logger.info('Closing database connection...');
    await orm.close(true); // Force close to ensure all connections are terminated
    logger.info('Database connection closed');
  }
}

// Execute the function
truncateTables()
  .then(() => {
    logger.info('Table truncation completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    logger.error('Table truncation failed:', error);
    process.exit(1);
  });
