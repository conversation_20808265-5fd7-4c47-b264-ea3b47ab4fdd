import { jest } from '@jest/globals';
import dotenv from 'dotenv';
import {
  mockScoutingRepository,
  mockScoutingRequestRepository,
} from './src/testing/mockRepositories.js';

// Optional: make jest globally available
globalThis.jest = jest;
dotenv.config();

jest.mock('./src/middleware/database/index.js');
jest.mock('./src/storage-interface/database-initializer.js', () => ({
  ...jest.requireActual('./src/storage-interface/database-initializer.js'),
  initializeDatabase: jest.fn().mockResolvedValue(),
  getScoutingRequestRepository: jest.fn().mockResolvedValue(mockScoutingRequestRepository),
  getScoutingRepository: jest.fn().mockResolvedValue(mockScoutingRepository),
}));
