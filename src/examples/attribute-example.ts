import { PlayerAttributes } from '../entities/PlayerAttributes.js';
import { BaseAttributeName, getAttributeProperties } from '../types/attribute-utils.js';

/**
 * Example function that demonstrates how to use the BaseAttributeName type
 * to work with player attributes
 */
export function processPlayerAttribute(
  playerAttributes: PlayerAttributes,
  attributeName: BaseAttributeName
): { current: number; potential: number } {
  // Get the property names for the current and potential values
  const properties = getAttributeProperties(attributeName);
  
  // Access the values using the property names
  const currentValue = playerAttributes[properties.current];
  const potentialValue = playerAttributes[properties.potential];
  
  return {
    current: currentValue,
    potential: potentialValue
  };
}

/**
 * Example of how to use BaseAttributeName as a type parameter
 */
export function getAttributeGrowthPotential<T extends BaseAttributeName>(
  playerAttributes: PlayerAttributes,
  attributeName: T
): number {
  const properties = getAttributeProperties(attributeName);
  
  const currentValue = playerAttributes[properties.current];
  const potentialValue = playerAttributes[properties.potential];
  
  return potentialValue - currentValue;
}

/**
 * Example of how to iterate over all base attribute names
 */
export function getAllAttributesGrowthPotential(
  playerAttributes: PlayerAttributes
): Record<BaseAttributeName, number> {
  // This is just an example of base attribute names
  // In a real application, you might want to derive this list dynamically
  const baseAttributes: BaseAttributeName[] = [
    'reflexes',
    'positioning',
    'shotStopping',
    'tackling',
    'marking',
    'heading',
    'finishing',
    'pace',
    'crossing',
    'passing',
    'vision',
    'ballControl'
  ];
  
  const result = {} as Record<BaseAttributeName, number>;
  
  for (const attr of baseAttributes) {
    result[attr] = getAttributeGrowthPotential(playerAttributes, attr);
  }
  
  return result;
}
