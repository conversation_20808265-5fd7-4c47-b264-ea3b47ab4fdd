import { generatePlayerAttributes } from '../functions/generate/player-attributes.js';
import { BaseAttributeName } from '../types/attribute-utils.js';
import { v4 as uuidv4 } from 'uuid';
import { Players } from '../entities/Players.js';

/**
 * Example function that demonstrates how to create a player with attributes
 */
export function createPlayerWithAttributes(
  firstName: string,
  surname: string,
  skillLevel: number,
  focusCategories: BaseAttributeName[]
): Players {
  // Create a new player
  const player = new Players();
  player.playerId = uuidv4();
  player.firstName = firstName;
  player.surname = surname;
  player.age = Math.floor(Math.random() * 23) + 17; // Random age between 17 and 39
  player.seed = Math.random();

  // Generate player attributes
  const attributes = generatePlayerAttributes(skillLevel, focusCategories, player.playerId);

  // Link the attributes to the player
  player.attributes = attributes;

  // Calculate player value (simplified example)
  player.value = calculatePlayerValue(player, attributes);

  return player;
}

/**
 * Simplified function to calculate player value based on attributes
 */
function calculatePlayerValue(player: Players, attributes: any): number {
  // Base value calculation (simplified)
  const baseValue = 100000;

  // Calculate average of all current attributes
  const attributeSum =
    attributes.reflexesCurrent +
    attributes.positioningCurrent +
    attributes.shotStoppingCurrent +
    attributes.tacklingCurrent +
    attributes.markingCurrent +
    attributes.headingCurrent +
    attributes.finishingCurrent +
    attributes.paceCurrent +
    attributes.crossingCurrent +
    attributes.passingCurrent +
    attributes.visionCurrent +
    attributes.ballControlCurrent;

  const attributeAvg = attributeSum / 12;

  // Value increases with attribute average
  const attributeFactor = Math.pow(attributeAvg / 40, 2);

  // Age factor: Younger players with high potential are more valuable
  const ageFactor = Math.max(0.5, 1 - (player.age - 25) * 0.05);

  return Math.round(baseValue * attributeFactor * ageFactor);
}

// Example usage
const player = createPlayerWithAttributes(
  'John',
  'Smith',
  20, // Skill level
  ['reflexes', 'positioning', 'shotStopping'] // Focus on goalkeeper attributes
);

console.log(`Created player: ${player.firstName} ${player.surname}`);
console.log(`Player value: ${player.value}`);
