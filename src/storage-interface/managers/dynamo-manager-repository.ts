import { Manager } from '@/entities/Manager.js';
import { DBManager } from '@/model/manager.js';
import DynamoDbService from '@/services/database/dynamo/dynamo-db-service.js';
import { logger } from '@/utils/logger.js';
import { ManagerRepository } from './manager-repository.js';

export class DynamoManagerRepository implements ManagerRepository {
  constructor(private readonly dynamoDb: DynamoDbService) {}

  async createManager(manager: Manager): Promise<void> {
    if (!process.env.MANAGERS_TABLE_NAME) {
      throw new Error('MANAGERS_TABLE_NAME environment variable not set');
    }

    try {
      // Convert Manager entity to DBManager
      const dbManager: DBManager = {
        managerId: manager.managerId,
        createdAt: manager.createdAt,
        lastActive: manager.lastActive,
        firstName: manager.firstName,
        lastName: manager.lastName,
        teamId: manager.team?.unwrap()?.teamId,
        gameworldId: manager.gameworldId,
        scoutTokens: manager.scoutTokens,
        superScoutTokens: manager.superScoutTokens,
      };

      await this.dynamoDb.insert(process.env.MANAGERS_TABLE_NAME, dbManager);
    } catch (error) {
      logger.error('Failed to create manager:', { error });
      throw error;
    }
  }

  async getManagerById(managerId: string): Promise<Manager | null> {
    if (!process.env.MANAGERS_TABLE_NAME) {
      throw new Error('MANAGERS_TABLE_NAME environment variable not set');
    }

    try {
      const dbManager = await this.dynamoDb.get<DBManager>(process.env.MANAGERS_TABLE_NAME, {
        managerId,
      });

      if (!dbManager) {
        return null;
      }

      // Convert DBManager to Manager entity
      const manager = new Manager();
      manager.managerId = dbManager.managerId;
      manager.createdAt = dbManager.createdAt;
      manager.lastActive = dbManager.lastActive;
      manager.firstName = dbManager.firstName;
      manager.lastName = dbManager.lastName;
      manager.gameworldId = dbManager.gameworldId;
      manager.scoutTokens = dbManager.scoutTokens;
      manager.superScoutTokens = dbManager.superScoutTokens;

      return manager;
    } catch (error) {
      logger.error('Failed to get manager by ID:', { error, managerId });
      throw error;
    }
  }

  async updateManager(managerId: string, updates: Partial<Manager>): Promise<void> {
    if (!process.env.MANAGERS_TABLE_NAME) {
      throw new Error('MANAGERS_TABLE_NAME environment variable not set');
    }

    try {
      // Convert updates to DBManager format
      const dbUpdates: Partial<DBManager> = {};
      
      if (updates.firstName !== undefined) dbUpdates.firstName = updates.firstName;
      if (updates.lastName !== undefined) dbUpdates.lastName = updates.lastName;
      if (updates.gameworldId !== undefined) dbUpdates.gameworldId = updates.gameworldId;
      if (updates.scoutTokens !== undefined) dbUpdates.scoutTokens = updates.scoutTokens;
      if (updates.superScoutTokens !== undefined) dbUpdates.superScoutTokens = updates.superScoutTokens;
      if (updates.lastActive !== undefined) dbUpdates.lastActive = updates.lastActive;
      if (updates.team !== undefined) {
        dbUpdates.teamId = updates.team.unwrap()?.teamId;
      }

      await this.dynamoDb.update(
        process.env.MANAGERS_TABLE_NAME,
        { managerId },
        dbUpdates
      );
    } catch (error) {
      logger.error('Failed to update manager:', { error, managerId });
      throw error;
    }
  }

  async deleteManager(managerId: string): Promise<void> {
    if (!process.env.MANAGERS_TABLE_NAME) {
      throw new Error('MANAGERS_TABLE_NAME environment variable not set');
    }

    try {
      await this.dynamoDb.delete(process.env.MANAGERS_TABLE_NAME, { managerId });
    } catch (error) {
      logger.error('Failed to delete manager:', { error, managerId });
      throw error;
    }
  }

  /**
   * Get a manager by user ID
   * @param userId The user ID
   */
  async getManager(userId: string): Promise<DBManager | null> {
    if (!process.env.MANAGERS_TABLE_NAME) {
      throw new Error('MANAGERS_TABLE_NAME environment variable not set');
    }

    try {
      return await this.dynamoDb.get<DBManager>(process.env.MANAGERS_TABLE_NAME, {
        managerId: userId,
      });
    } catch (error) {
      logger.error('Failed to get manager:', { error, userId });
      throw error;
    }
  }
}
