import { Manager } from '@/entities/Manager.js';
import { MikroOrmService } from '@/storage-interface/mikro-orm-service.js';
import { logger } from '@/utils/logger.js';
import { ManagerRepository } from './manager-repository.js';

export class MikroOrmManagerRepository implements ManagerRepository {
  constructor(private mikroOrmService: MikroOrmService) {}

  async createManager(manager: Manager): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      em.persist(manager);
      await em.flush();
    } catch (error) {
      logger.error('Failed to create manager:', { error });
      throw error;
    }
  }

  async getManagerById(managerId: string): Promise<Manager | null> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      return await em.findOne(Manager, { managerId });
    } catch (error) {
      logger.error('Failed to get manager by ID:', { error, managerId });
      throw error;
    }
  }

  async updateManager(managerId: string, updates: Partial<Manager>): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      const manager = await em.findOne(Manager, { managerId });

      if (!manager) {
        throw new Error(`Manager with ID ${managerId} not found`);
      }

      em.assign(manager, updates);
      await em.flush();
    } catch (error) {
      logger.error('Failed to update manager:', { error, managerId });
      throw error;
    }
  }

  async deleteManager(managerId: string): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      const manager = await em.findOne(Manager, { managerId });

      if (manager) {
        em.remove(manager);
        await em.flush();
      }
    } catch (error) {
      logger.error('Failed to delete manager:', { error, managerId });
      throw error;
    }
  }

  async updateNotificationPreferences(
    managerId: string,
    preferences: Partial<NotificationPreferences>
  ): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      const manager = await em.findOne(Manager, { managerId });

      if (!manager) {
        throw new Error(`Manager with ID ${managerId} not found`);
      }

      manager.notificationPreferences = {
        ...manager.notificationPreferences,
        ...preferences,
      };

      await em.persistAndFlush(manager);
    } catch (error) {
      logger.error('Failed to update manager notification preferences:', { error, managerId });
      throw error;
    }
  }
}
