import { Manager } from '@/entities/Manager.js';

export interface ManagerRepository {
  /**
   * Create a new manager
   * @param manager The manager to create
   */
  createManager(manager: Manager): Promise<void>;

  /**
   * Get a manager by ID
   * @param managerId The ID of the manager to get
   */
  getManagerById(managerId: string): Promise<Manager | null>;

  /**
   * Update a manager
   * @param managerId The ID of the manager to update
   * @param updates The updates to apply
   */
  updateManager(managerId: string, updates: Partial<Manager>): Promise<void>;

  /**
   * Delete a manager
   * @param managerId The ID of the manager to delete
   */
  deleteManager(managerId: string): Promise<void>;
}
