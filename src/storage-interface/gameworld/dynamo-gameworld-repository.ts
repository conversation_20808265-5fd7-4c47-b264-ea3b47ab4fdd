import { Gameworld } from '@/entities/Gameworld.js';
import DynamoDbService from '@/services/database/dynamo/dynamo-db-service.js';
import { logger } from '@/utils/logger.js';
import { GameworldRepository } from './gameworld-repository.interface.js';

export class DynamoGameworldRepository implements GameworldRepository {
  getCompletedSeasons(): Promise<Gameworld[]> {
    return Promise.resolve([]);
  }
  constructor(private readonly dynamoDb: DynamoDbService) {}

  async getGameworld(gameworldId: string): Promise<Gameworld | null> {
    try {
      const result = await this.dynamoDb.get(process.env.GAMEWORLD_TABLE_NAME!, {
        id: gameworldId,
      });

      if (!result) {
        return null;
      }

      return this.mapDbItemToGameworld(result);
    } catch (error) {
      logger.error('Failed to get gameworld:', { error, gameworldId });
      throw error;
    }
  }

  async getAllGameworlds(): Promise<Gameworld[]> {
    try {
      const result = await this.dynamoDb.scan(process.env.GAMEWORLD_TABLE_NAME!);

      if (!result || !result.items || result.items.length === 0) {
        return [];
      }

      return result.items.map((item) => this.mapDbItemToGameworld(item));
    } catch (error) {
      logger.error('Failed to get all gameworlds:', { error });
      throw error;
    }
  }

  async createGameworld(gameworld: Gameworld): Promise<void> {
    try {
      const item = {
        id: gameworld.id,
        endDate: gameworld.endDate,
      };

      await this.dynamoDb.put(process.env.GAMEWORLD_TABLE_NAME!, item);
    } catch (error) {
      logger.error('Failed to create gameworld:', { error, gameworld });
      throw error;
    }
  }

  async updateGameworld(gameworld: Gameworld): Promise<void> {
    try {
      await this.dynamoDb.update(
        process.env.GAMEWORLD_TABLE_NAME!,
        { id: gameworld.id },
        { endDate: gameworld.endDate }
      );
    } catch (error) {
      logger.error('Failed to update gameworld:', { error, gameworld });
      throw error;
    }
  }

  /**
   * Helper method to map a DynamoDB item to a Gameworld entity
   * @param item The DynamoDB item
   * @returns A Gameworld entity
   */
  private mapDbItemToGameworld(item: any): Gameworld {
    const gameworld = new Gameworld();
    gameworld.id = item.id;
    gameworld.endDate = item.endDate;

    return gameworld;
  }
}
