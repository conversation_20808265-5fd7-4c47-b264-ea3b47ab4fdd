import { logger } from '@/utils/logger.js';
import { MikroORM } from '@mikro-orm/core';
import {
  AnyEntity,
  EntityClass,
  EntityManager,
  EntityRepository,
  PostgreSqlDriver,
} from '@mikro-orm/postgresql';

export class MikroOrmService {
  private orm: MikroORM<PostgreSqlDriver> | undefined;
  private initialized: boolean = false;

  constructor(private readonly orm$: Promise<MikroORM<PostgreSqlDriver>>) {}

  /**
   * Initialize the MikroORM connection
   */
  async initialize(): Promise<void> {
    try {
      if (!this.initialized) {
        this.orm = await this.orm$;
        this.initialized = true;
        logger.info('MikroORM database connection initialized');
      } else {
        logger.debug('MikroORM database connection already initialized');
      }
    } catch (error) {
      logger.error('Error initializing MikroORM database connection:', { error });
      throw error;
    }
  }

  /**
   * Check if the service is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Get the entity manager
   */
  getEntityManager(): EntityManager {
    if (!this.initialized) {
      throw new Error('MikroORM service not initialized');
    }
    return this.orm!.em.fork();
  }

  /**
   * Get a repository for an entity
   * @param entity The entity class
   */
  getRepository<T extends AnyEntity>(entity: EntityClass<T>): EntityRepository<T> {
    if (!this.initialized) {
      throw new Error('MikroORM service not initialized');
    }
    return this.orm!.em.fork().getRepository<T>(entity);
  }

  /**
   * Close the MikroORM connection
   */
  async close(): Promise<void> {
    if (this.initialized) {
      await this.orm!.close();
      this.initialized = false;
    }
  }
}
