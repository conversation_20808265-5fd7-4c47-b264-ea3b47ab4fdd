import { Player } from '@/entities/Player.js';
import { ScoutedPlayer } from '@/entities/ScoutedPlayer.js';
import { Team } from '@/entities/Team.js';
import { MikroOrmService } from '@/storage-interface/mikro-orm-service.js';
import { PlayerRepository } from '@/storage-interface/players/player-repository.interface.js';
import { ScoutingRepository } from '@/storage-interface/scouting/scouting-repository.interface.js';
import { logger } from '@/utils/logger.js';
import { Loaded, Reference } from '@mikro-orm/core';

/**
 * MikroORM implementation of the ScoutingRepository
 */
export class MikroOrmScoutingRepository implements ScoutingRepository {
  constructor(
    private mikroOrmService: MikroOrmService,
    private playerRepository: PlayerRepository
  ) {}

  /**
   * Get random players from a league and mark them as scouted by a team
   * @param gameworldId The gameworld ID
   * @param leagueId The league ID
   * @param teamId The team ID
   * @param count The number of players to scout
   * @returns Array of scouted players
   */
  async scoutRandomPlayersFromLeague(
    gameworldId: string,
    leagueId: string,
    teamId: string,
    count: number
  ): Promise<Player[]> {
    try {
      // Get random players from the league
      const randomPlayers = await this.playerRepository.getRandomPlayersFromLeague(
        gameworldId,
        leagueId,
        count
      );

      if (randomPlayers.length === 0) {
        logger.warn('No players found in league for scouting', { gameworldId, leagueId, teamId });
        return [];
      }

      // Save the players as scouted
      const playerIds = randomPlayers.map((p) => p.playerId);
      await this.saveScoutedPlayers(gameworldId, teamId, playerIds);

      logger.debug('Scouted players from league', {
        gameworldId,
        leagueId,
        teamId,
        playerCount: randomPlayers.length,
        playerIds: randomPlayers.map((p) => p.playerId),
      });

      return randomPlayers;
    } catch (error) {
      logger.error('Error scouting random players from league', {
        gameworldId,
        leagueId,
        teamId,
        count,
        error,
      });
      throw error;
    }
  }

  /**
   * Get players from a team that haven't been scouted yet by the scouting team
   * @param gameworldId The gameworld ID
   * @param targetTeamId The ID of the team being scouted
   * @param scoutingTeamId The ID of the team doing the scouting
   * @param count The maximum number of players to scout
   * @returns Array of players to scout
   */
  async scoutPlayersFromTeam(
    gameworldId: string,
    targetTeamId: string,
    scoutingTeamId: string,
    count: number
  ): Promise<Player[]> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // Get all players in the target team
      const allPlayers = await em.find(Player, {
        gameworldId,
        team: targetTeamId,
      });

      if (allPlayers.length === 0) {
        logger.warn('No players found in team for scouting', { gameworldId, targetTeamId });
        return [];
      }

      // Get all players already scouted by the scouting team
      const scoutedPlayers = await em.find(ScoutedPlayer, {
        gameworldId,
        team: scoutingTeamId,
      });

      // Create a set of already scouted player IDs for faster lookup
      const scoutedPlayerIds = new Set<string>();
      scoutedPlayers.forEach((sp) => {
        if (sp.player) {
          const playerId = typeof sp.player === 'string' ? sp.player : sp.player.playerId;
          scoutedPlayerIds.add(playerId);
        }
      });

      // Filter out players that have already been scouted
      const unscoutedPlayers = allPlayers.filter((player) => !scoutedPlayerIds.has(player.playerId));

      // Get up to 'count' unscouted players
      const playersToScout = unscoutedPlayers.slice(0, count);

      if (playersToScout.length === 0) {
        logger.debug('All players have already been scouted by this team', {
          gameworldId,
          targetTeamId,
          scoutingTeamId,
        });
        return [];
      }

      // Save the players as scouted
      const playerIds = playersToScout.map((p) => p.playerId);
      await this.saveScoutedPlayers(gameworldId, scoutingTeamId, playerIds);

      logger.debug('Scouted players from team', {
        gameworldId,
        targetTeamId,
        scoutingTeamId,
        playerCount: playersToScout.length,
        playerIds,
      });

      return playersToScout;
    } catch (error) {
      logger.error('Error scouting players from team', {
        gameworldId,
        targetTeamId,
        scoutingTeamId,
        count,
        error,
      });
      throw error;
    }
  }

  /**
   * Check if a player has been scouted by a specific team
   * @param gameworldId The gameworld ID
   * @param playerId The player ID
   * @param teamId The team ID
   * @returns True if the player has been scouted by the team, false otherwise
   */
  async isPlayerScoutedByTeam(
    gameworldId: string,
    playerId: string,
    teamId: string
  ): Promise<boolean> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // Find the player and team entities first
      const player = await em.findOne(Player, { gameworldId, playerId });
      if (!player) {
        logger.warn('Player not found when checking if scouted', { gameworldId, playerId, teamId });
        return false;
      }

      // Check if there's a scouted player record
      const scoutedPlayer = await em.findOne(ScoutedPlayer, {
        gameworldId,
        team: teamId,
        player: playerId,
      });

      return !!scoutedPlayer;
    } catch (error) {
      logger.error('Error checking if player is scouted by team', {
        gameworldId,
        playerId,
        teamId,
        error,
      });
      throw error;
    }
  }

  /**
   * Get all players scouted by a specific team
   * @param gameworldId The gameworld ID
   * @param teamId The team ID
   * @param limit The number of players to return
   * @param lastEvaluatedKey The last evaluated key
   * @returns Array of scouted players
   */
  async getPlayersScoutedByTeam(
    gameworldId: string,
    teamId: string,
    limit?: number,
    lastEvaluatedKey?: string
  ): Promise<Loaded<ScoutedPlayer, 'player' | 'player.attributes'>[]> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // Find all scouted players for the team
      return await em.find(
        ScoutedPlayer,
        {
          gameworldId,
          team: teamId,
        },
        {
          populate: ['player', 'player.attributes'],
          orderBy: { scoutedAt: 'DESC' },
          limit: limit,
          offset: lastEvaluatedKey ? Number.parseInt(lastEvaluatedKey) : undefined,
        }
      );
    } catch (error) {
      logger.error('Error getting players scouted by team', { gameworldId, teamId, error });
      throw error;
    }
  }

  /**
   * Save players as scouted by a team
   * @param gameworldId The gameworld ID
   * @param teamId The team ID
   * @param playerIds Array of player IDs to mark as scouted
   * @returns Promise that resolves when the players are saved as scouted
   */
  async saveScoutedPlayers(gameworldId: string, teamId: string, playerIds: string[]): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      const currentTime = Date.now();

      // Create scouted player records for each player ID
      for (const playerId of playerIds) {
        // Check if the player is already scouted
        const existingScoutedPlayer = await em.findOne(ScoutedPlayer, {
          gameworldId,
          team: teamId,
          player: playerId,
        });

        if (existingScoutedPlayer) {
          // Update the scouted timestamp
          existingScoutedPlayer.scoutedAt = currentTime;
          em.persist(existingScoutedPlayer);
        } else {
          // Create a new scouted player record
          const scoutedPlayer = new ScoutedPlayer();
          scoutedPlayer.gameworldId = gameworldId;
          scoutedPlayer.team = Reference.createFromPK(Team, teamId);
          scoutedPlayer.player = Reference.createFromPK(Player, playerId);
          scoutedPlayer.scoutedAt = currentTime;
          em.persist(scoutedPlayer);
        }
      }

      // Save all changes
      await em.flush();

      logger.debug('Saved scouted players', {
        gameworldId,
        teamId,
        playerCount: playerIds.length,
        playerIds,
      });
    } catch (error) {
      logger.error('Error saving scouted players', {
        gameworldId,
        teamId,
        playerIds,
        error,
      });
      throw error;
    }
  }
}
