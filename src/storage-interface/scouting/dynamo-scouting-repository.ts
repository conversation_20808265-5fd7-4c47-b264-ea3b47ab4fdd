import { Player } from '@/entities/Player.js';
import { ScoutedPlayer } from '@/entities/ScoutedPlayer.js';
import { Team } from '@/entities/Team.js';
import { ScoutedPlayersRecord } from '@/model/scouted-player.js';
import DynamoDbService from '@/services/database/dynamo/dynamo-db-service.js';
import { PlayerRepository } from '@/storage-interface/players/player-repository.interface.js';
import { ScoutingRepository } from '@/storage-interface/scouting/scouting-repository.interface.js';
import { logger } from '@/utils/logger.js';
import { Loaded, Reference } from '@mikro-orm/core';

/**
 * DynamoDB implementation of the ScoutingRepository
 */
export class DynamoScoutingRepository implements ScoutingRepository {
  constructor(
    private dynamoDb: DynamoDbService,
    private playerRepository: PlayerRepository
  ) {}

  /**
   * Get random players from a league and mark them as scouted by a team
   * @param gameworldId The gameworld ID
   * @param leagueId The league ID
   * @param teamId The team ID
   * @param count The number of players to scout
   * @returns Array of scouted players
   */
  async scoutRandomPlayersFromLeague(
    gameworldId: string,
    leagueId: string,
    teamId: string,
    count: number
  ): Promise<Player[]> {
    try {
      // Get random players from the league
      const randomPlayers = await this.playerRepository.getRandomPlayersFromLeague(
        gameworldId,
        leagueId,
        count
      );

      if (randomPlayers.length === 0) {
        logger.warn('No players found in league for scouting', { gameworldId, leagueId, teamId });
        return [];
      }

      // Save the players as scouted
      const playerIds = randomPlayers.map((p) => p.playerId);
      await this.saveScoutedPlayers(gameworldId, teamId, playerIds);

      logger.debug('Scouted players from league', {
        gameworldId,
        leagueId,
        teamId,
        playerCount: randomPlayers.length,
        playerIds,
      });

      return randomPlayers;
    } catch (error) {
      logger.error('Error scouting random players from league', {
        gameworldId,
        leagueId,
        teamId,
        count,
        error,
      });
      throw error;
    }
  }

  /**
   * Get players from a team that haven't been scouted yet by the scouting team
   * @param gameworldId The gameworld ID
   * @param targetTeamId The ID of the team being scouted
   * @param scoutingTeamId The ID of the team doing the scouting
   * @param count The maximum number of players to scout
   * @returns Array of players to scout
   */
  async scoutPlayersFromTeam(
    gameworldId: string,
    targetTeamId: string,
    scoutingTeamId: string,
    count: number
  ): Promise<Player[]> {
    try {
      if (!process.env.PLAYERS_TABLE_NAME) {
        throw new Error('PLAYERS_TABLE_NAME environment variable is not set');
      }

      // Get all players in the target team
      const allPlayers: Player[] = [];
      let lastEvaluatedKey: Record<string, any> | undefined = undefined;

      do {
        const result = (await this.dynamoDb.query<Player>(
          process.env.PLAYERS_TABLE_NAME,
          {
            hashKey: {
              name: 'teamId',
              value: targetTeamId,
            },
            rangeKey: {
              name: 'gameworldId',
              value: gameworldId,
            },
          },
          'teamIndex',
          {
            exclusiveStartKey: lastEvaluatedKey,
          }
        )) as QueryResult<Player>;

        if (result?.items) {
          allPlayers.push(...result.items);
        }

        lastEvaluatedKey = result?.lastEvaluatedKey;
      } while (lastEvaluatedKey);

      if (allPlayers.length === 0) {
        logger.warn('No players found in team for scouting', { gameworldId, targetTeamId });
        return [];
      }

      // Get the scouted players record for the scouting team
      const scoutedPlayersRecord = await this.dynamoDb.get<ScoutedPlayersRecord>(
        process.env.SCOUTED_PLAYERS_TABLE_NAME!,
        {
          gameworldId,
          teamId: scoutingTeamId,
        }
      );

      // Create a set of already scouted player IDs for faster lookup
      const scoutedPlayerIds = new Set<string>();
      if (
        scoutedPlayersRecord &&
        scoutedPlayersRecord.scoutedPlayers &&
        scoutedPlayersRecord.scoutedPlayers.length > 0
      ) {
        scoutedPlayersRecord.scoutedPlayers.forEach((p) => scoutedPlayerIds.add(p.playerId));
      }

      // Filter out players that have already been scouted
      const unscoutedPlayers = allPlayers.filter((player) => !scoutedPlayerIds.has(player.playerId));

      // Get up to 'count' unscouted players
      const playersToScout = unscoutedPlayers.slice(0, count);

      if (playersToScout.length === 0) {
        logger.debug('All players have already been scouted by this team', {
          gameworldId,
          targetTeamId,
          scoutingTeamId,
        });
        return [];
      }

      // Save the players as scouted
      const playerIds = playersToScout.map((p) => p.playerId);
      await this.saveScoutedPlayers(gameworldId, scoutingTeamId, playerIds);

      logger.debug('Scouted players from team', {
        gameworldId,
        targetTeamId,
        scoutingTeamId,
        playerCount: playersToScout.length,
        playerIds,
      });

      return playersToScout;
    } catch (error) {
      logger.error('Error scouting players from team', {
        gameworldId,
        targetTeamId,
        scoutingTeamId,
        count,
        error,
      });
      throw error;
    }
  }

  /**
   * Check if a player has been scouted by a specific team
   * @param gameworldId The gameworld ID
   * @param playerId The player ID
   * @param teamId The team ID
   * @returns True if the player has been scouted by the team, false otherwise
   */
  async isPlayerScoutedByTeam(
    gameworldId: string,
    playerId: string,
    teamId: string
  ): Promise<boolean> {
    try {
      // Get the scouted players record for the team
      const scoutedPlayersRecord = await this.dynamoDb.get<ScoutedPlayersRecord>(
        process.env.SCOUTED_PLAYERS_TABLE_NAME!,
        {
          gameworldId,
          teamId,
        }
      );

      // If no record exists or it's empty, the player hasn't been scouted
      if (!scoutedPlayersRecord || !scoutedPlayersRecord.scoutedPlayers) {
        return false;
      }

      // Check if the player is in the scouted players list
      return scoutedPlayersRecord.scoutedPlayers.some((player) => player.playerId === playerId);
    } catch (error) {
      logger.error('Error checking if player is scouted by team', {
        gameworldId,
        playerId,
        teamId,
        error,
      });
      throw error;
    }
  }

  /**
   * Get all players scouted by a specific team
   * @param gameworldId The gameworld ID
   * @param teamId The team ID
   * @param limit The maximum number of players to return
   * @param lastEvaluatedKey The last evaluated key for pagination
   * @returns Array of scouted players
   */
  async getPlayersScoutedByTeam(
    gameworldId: string,
    teamId: string,
    limit?: number,
    lastEvaluatedKey?: string
  ): Promise<Loaded<ScoutedPlayer, 'player' | 'player.attributes'>[]> {
    try {
      // Get the scouted players record for the team
      const scoutedPlayersRecord = await this.dynamoDb.get<ScoutedPlayersRecord>(
        process.env.SCOUTED_PLAYERS_TABLE_NAME!,
        {
          gameworldId,
          teamId,
        }
      );

      // If no record exists or it's empty, return an empty array
      if (!scoutedPlayersRecord || !scoutedPlayersRecord.scoutedPlayers) {
        return [];
      }

      // Get the player IDs from the scouted players record
      const playerIds = scoutedPlayersRecord.scoutedPlayers.map(p => p.playerId);

      // Create a map of player ID to timestamp for quick lookup
      const timestampMap = new Map<string, number>();
      scoutedPlayersRecord.scoutedPlayers.forEach(p => {
        timestampMap.set(p.playerId, p.timestamp);
      });

      // Fetch the actual player entities
      // Note: This is a simplified implementation. In a real implementation,
      // you would need to fetch the players in batches if there are many.
      const players: Player[] = [];
      for (const playerId of playerIds) {
        const player = await this.playerRepository.getPlayer(gameworldId, playerId);
        if (player) {
          players.push(player);
        }
      }

      // Convert to ScoutedPlayer entities
      const scoutedPlayers: any[] = players.map(player => {
        const scoutedPlayer = new ScoutedPlayer();
        scoutedPlayer.gameworldId = gameworldId;
        scoutedPlayer.team = Reference.createFromPK(Team, teamId);
        scoutedPlayer.player = Reference.createFromPK(Player, player.playerId);
        scoutedPlayer.scoutedAt = timestampMap.get(player.playerId) || Date.now();
        return scoutedPlayer;
      });

      // Apply limit if specified
      if (limit && limit > 0 && scoutedPlayers.length > limit) {
        return scoutedPlayers.slice(0, limit);
      }

      return scoutedPlayers;
    } catch (error) {
      logger.error('Error getting players scouted by team', { gameworldId, teamId, error });
      throw error;
    }
  }

  /**
   * Save players as scouted by a team
   * @param gameworldId The gameworld ID
   * @param teamId The team ID
   * @param playerIds Array of player IDs to mark as scouted
   * @returns Promise that resolves when the players are saved as scouted
   */
  async saveScoutedPlayers(gameworldId: string, teamId: string, playerIds: string[]): Promise<void> {
    return this.updateScoutedPlayersByIds(gameworldId, teamId, playerIds);
  }

  /**
   * Update scouted players by IDs
   * @param gameworldId The gameworld ID
   * @param teamId The team ID
   * @param playerIds Array of player IDs to mark as scouted
   */
  private async updateScoutedPlayersByIds(
    gameworldId: string,
    teamId: string,
    playerIds: string[]
  ): Promise<void> {
    if (!process.env.SCOUTED_PLAYERS_TABLE_NAME) {
      throw new Error('SCOUTED_PLAYERS_TABLE_NAME environment variable is not set');
    }

    const currentTime = Date.now();

    try {
      // Try to get existing record
      const existingRecord = await this.dynamoDb.get<ScoutedPlayersRecord>(
        process.env.SCOUTED_PLAYERS_TABLE_NAME,
        {
          gameworldId,
          teamId,
        }
      );

      // Initialize scoutedPlayers array
      const scoutedPlayers =
        existingRecord && existingRecord.scoutedPlayers ? [...existingRecord.scoutedPlayers] : [];

      // Add new players or update timestamps for existing ones
      for (const playerId of playerIds) {
        const existingIndex = scoutedPlayers.findIndex((p) => p.playerId === playerId);

        if (existingIndex >= 0) {
          // Update timestamp for existing player
          scoutedPlayers[existingIndex]!.timestamp = currentTime;
        } else {
          // Add new player
          scoutedPlayers.push({
            playerId,
            timestamp: currentTime,
          });
        }
      }

      // Save the updated record
      const record: ScoutedPlayersRecord = {
        gameworldId,
        teamId,
        scoutedPlayers,
      };

      await this.dynamoDb.insert(process.env.SCOUTED_PLAYERS_TABLE_NAME, record);

      logger.debug('Updated scouted players record in DynamoDB', {
        gameworldId,
        teamId,
        scoutedPlayersCount: scoutedPlayers.length,
      });
    } catch (error) {
      logger.error('Failed to update scouted players in DynamoDB', {
        gameworldId,
        teamId,
        error,
      });
      throw error;
    }
  }
}
