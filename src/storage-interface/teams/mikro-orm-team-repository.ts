import { AvailableTeam } from '@/entities/AvailableTeam.js';
import { Fixture } from '@/entities/Fixture.js';
import { League } from '@/entities/League.js';
import { Team } from '@/entities/Team.js';
import { TeamMovement } from '@/functions/league/logic/LeagueProcessor.js';
import { LeagueStandings } from '@/model/team.js';
import { MikroOrmService } from '@/storage-interface/mikro-orm-service.js';
import { TeamRepository } from '@/storage-interface/teams/team-repository.interface.js';
import { logger } from '@/utils/logger.js';
import { FilterQuery, raw, Reference } from '@mikro-orm/core';

export class MikroOrmTeamRepository implements TeamRepository {
  constructor(private mikroOrmService: MikroOrmService) {}

  async batchInsertTeams(teams: Team[]): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      for (const team of teams) {
        em.persist(team);
      }
      await em.flush();
    } catch (error) {
      logger.error('Failed to create teams:', { error });
      throw error;
    }
  }

  async batchInsertAvailableTeams(teams: AvailableTeam[]): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      for (const team of teams) {
        em.persist(team);
      }
      await em.flush();
    } catch (error) {
      logger.error('Failed to create available teams:', { error });
      throw error;
    }
  }

  async getTeamsByGameworld(gameworldId: string, includePlayers: boolean = false): Promise<Team[]> {
    const em = this.mikroOrmService.getEntityManager();
    const where: FilterQuery<Team> = { gameworldId };
    return em.find(
      Team,
      where,
      includePlayers
        ? {
            populate: ['players', 'players.attributes'],
            exclude: ['players.team'],
          }
        : undefined
    );
  }

  async getTeamsByLeague(leagueId: string, includePlayers: boolean = false): Promise<Team[]> {
    const em = this.mikroOrmService.getEntityManager();
    const where: FilterQuery<Team> = { league: leagueId };
    return em.find(
      Team,
      where,
      includePlayers
        ? {
            populate: ['players', 'players.attributes'],
            exclude: ['players.team'],
          }
        : undefined
    );
  }

  async updateTeamStandings(
    teamId: string,
    gameworldId: string,
    standings: LeagueStandings,
    flush = true
  ): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    const team = await em.findOne(Team, { teamId, gameworldId });
    if (!team) {
      throw new Error(`Team not found: ${teamId}`);
    }

    team.points = standings.points;
    team.goalsFor = standings.goalsFor;
    team.goalsAgainst = standings.goalsAgainst;
    team.wins = standings.wins;
    team.draws = standings.draws;
    team.losses = standings.losses;
    team.played = standings.played;

    if (flush) {
      return em.persistAndFlush(team);
    } else {
      em.persist(team);
    }
  }

  async updateTeamLeague(teamId: string, gameworldId: string, newLeagueId: string): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    const where: FilterQuery<Team> = { teamId, gameworldId };
    const team = await em.findOne(Team, where);

    if (team) {
      team.league = Reference.createFromPK(League, newLeagueId);
      team.points = 0;
      team.goalsFor = 0;
      team.goalsAgainst = 0;
      team.wins = 0;
      team.draws = 0;
      team.losses = 0;
      team.played = 0;

      await em.persistAndFlush(team);
    }
  }

  async resetTeamStandings(teamId: string, gameworldId: string): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    const where: FilterQuery<Team> = { teamId, gameworldId };
    const team = await em.findOne(Team, where);

    if (team) {
      team.points = 0;
      team.goalsFor = 0;
      team.goalsAgainst = 0;
      team.wins = 0;
      team.draws = 0;
      team.losses = 0;
      team.played = 0;

      await em.persistAndFlush(team);
    }
  }

  async updateTeamLeagues(
    teams: Team[],
    movements: TeamMovement[],
    gameworldId: string
  ): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // Start a transaction
      await em.transactional(async (txEm) => {
        // Process team movements
        for (const movement of movements) {
          const where: FilterQuery<Team> = { teamId: movement.teamId, gameworldId };
          const team = await txEm.findOne(Team, where);

          if (team) {
            team.league = Reference.createFromPK(League, movement.toLeagueId);
            team.points = 0;
            team.goalsFor = 0;
            team.goalsAgainst = 0;
            team.wins = 0;
            team.draws = 0;
            team.losses = 0;
            team.played = 0;

            txEm.persist(team);
          }
        }

        // Reset standings for stationary teams
        const stationaryTeams = teams.filter(
          (team) => !movements.some((movement) => movement.teamId === team.teamId)
        );

        for (const team of stationaryTeams) {
          const where: FilterQuery<Team> = { teamId: team.teamId, gameworldId };
          const dbTeam = await txEm.findOne(Team, where);

          if (dbTeam) {
            dbTeam.points = 0;
            dbTeam.goalsFor = 0;
            dbTeam.goalsAgainst = 0;
            dbTeam.wins = 0;
            dbTeam.draws = 0;
            dbTeam.losses = 0;
            dbTeam.played = 0;

            txEm.persist(dbTeam);
          }
        }
      });

      logger.info('Successfully updated team leagues and reset standings', {
        totalTeams: teams.length,
        movedTeams: movements.length,
        stationaryTeams: teams.length - movements.length,
      });
    } catch (error) {
      logger.error('Failed to update team leagues', { error });
      throw error;
    }
  }

  async getRandomAvailableTeam(): Promise<AvailableTeam | null> {
    const em = this.mikroOrmService.getEntityManager();
    const qb = em.qb(AvailableTeam, 'at');

    const [availableTeam] = await qb
      .orderBy({ [raw(`RANDOM()`)]: 'ASC' })
      .limit(1)
      .getResult();

    return availableTeam || null;
  }

  async deleteAvailableTeam(team: AvailableTeam): Promise<number> {
    const em = this.mikroOrmService.getEntityManager();
    return em.nativeDelete(AvailableTeam, { id: team.id });
  }

  async getTeam(
    gameworldId: string,
    teamId: string,
    includePlayers: boolean
  ): Promise<Team | null> {
    const em = this.mikroOrmService.getEntityManager();
    return await em.findOne(
      Team,
      { teamId, gameworldId },
      includePlayers
        ? {
            populate: [
              'players',
              'players.attributes',
              'players.matchHistory',
              'players.overallStats',
            ],
            exclude: ['players.team'],
          }
        : undefined
    );
  }

  async getTeamAndNextMatch(
    gameworldId: string,
    teamId: string,
    includePlayers: boolean
  ): Promise<{ team: Team | null; nextFixture: Fixture | null }> {
    const em = this.mikroOrmService.getEntityManager();

    // Get the team with optional player details
    const team = await em.findOne(
      Team,
      { teamId, gameworldId },
      includePlayers
        ? {
            populate: ['players', 'players.attributes'],
            exclude: ['players.team'],
          }
        : undefined
    );

    // Find the next unplayed fixture for this team
    const nextFixture = await em.findOne(
      Fixture,
      {
        gameworldId,
        played: false,
        $or: [{ homeTeam: { teamId } }, { awayTeam: { teamId } }],
      },
      {
        orderBy: { date: 'ASC' },
      }
    );

    return { team, nextFixture };
  }

  /**
   * Update a team's balance
   * @param teamId The team ID
   * @param gameworldId The gameworld ID
   * @param amount The amount to add to the current balance
   */
  async updateTeamBalance(teamId: string, gameworldId: string, amount: number): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    const result = await em.nativeUpdate(
      Team,
      { teamId, gameworldId },
      { balance: raw(`balance + ${amount}`) }
    );
    if (result === 0) {
      throw new Error(`Team not found: ${teamId}`);
    }
  }

  /**
   * Get all teams without a manager in a specific gameworld
   * @param includePlayers Whether to include players in the response
   */
  async getTeamsWithoutManager(includePlayers: boolean = false): Promise<Team[]> {
    const em = this.mikroOrmService.getEntityManager();
    const where: FilterQuery<Team> = {
      manager: null,
    };

    return em.find(
      Team,
      where,
      includePlayers
        ? {
            populate: ['players', 'players.attributes'],
            exclude: ['players.team'],
          }
        : undefined
    );
  }

  /**
   * Update a team's selection order
   * @param teamId The team ID
   * @param gameworldId The gameworld ID
   * @param selectionOrder The new selection order array of player IDs
   */
  async updateTeamSelectionOrder(
    teamId: string,
    gameworldId: string,
    selectionOrder: string[]
  ): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    const team = await em.findOne(Team, { teamId, gameworldId });
    if (!team) {
      throw new Error(`Team not found: ${teamId}`);
    }

    team.selectionOrder = selectionOrder;
    await em.persistAndFlush(team);
  }

  /**
   * Find multiple teams by their IDs
   * @param teamIds Array of team IDs to find
   * @param includePlayers Whether to include players in the response
   * @returns Array of teams matching the provided IDs
   */
  async findByIds(teamIds: string[], includePlayers: boolean = false): Promise<Team[]> {
    const em = this.mikroOrmService.getEntityManager();
    const where: FilterQuery<Team> = { teamId: { $in: teamIds } };

    return em.find(
      Team,
      where,
      includePlayers
        ? {
            populate: ['players', 'players.attributes'],
            exclude: ['players.team'],
          }
        : undefined
    );
  }

  async flush() {
    const em = this.mikroOrmService.getEntityManager();
    await em.flush();
  }
}
