import { AvailableTeam } from '@/entities/AvailableTeam.js';
import { League } from '@/entities/League.js';
import { Team } from '@/entities/Team.js';
import { TeamMovement } from '@/functions/league/logic/LeagueProcessor.js';
import { LeagueStandings } from '@/model/team.js';
import DynamoDbService from '@/services/database/dynamo/dynamo-db-service.js';
import { TeamRepository } from '@/storage-interface/teams/team-repository.interface.js';
import { logger } from '@/utils/logger.js';
import { Reference } from '@mikro-orm/core';

export class DynamoTeamRepository implements TeamRepository {
  updateTeamStandings(
    teamId: string,
    gameworldId: string,
    standings: LeagueStandings
  ): Promise<void> {
    return Promise.resolve(undefined);
  }
  getTeam(
    gameworldId: string,
    teamId: string,
    includePlayers: boolean | undefined
  ): Promise<Team | null> {
    return Promise.resolve(undefined);
  }

  /**
   * Update a team's balance
   * @param teamId The team ID
   * @param gameworldId The gameworld ID
   * @param balance The new balance
   */
  async updateTeamBalance(teamId: string, gameworldId: string, balance: number): Promise<void> {
    if (!process.env.TEAMS_TABLE_NAME) {
      throw new Error('TEAMS_TABLE_NAME environment variable not set');
    }

    try {
      await this.dynamoDb.update(
        process.env.TEAMS_TABLE_NAME,
        { teamId, gameworldId },
        { balance }
      );
    } catch (error) {
      logger.error('Failed to update team balance', { teamId, gameworldId, balance, error });
      throw error;
    }
  }

  /**
   * Update a team's selection order
   * @param teamId The team ID
   * @param gameworldId The gameworld ID
   * @param selectionOrder The new selection order array of player IDs
   */
  async updateTeamSelectionOrder(
    teamId: string,
    gameworldId: string,
    selectionOrder: string[]
  ): Promise<void> {
    if (!process.env.TEAMS_TABLE_NAME) {
      throw new Error('TEAMS_TABLE_NAME environment variable not set');
    }

    try {
      await this.dynamoDb.update(
        process.env.TEAMS_TABLE_NAME,
        { teamId, gameworldId },
        { selectionOrder }
      );
    } catch (error) {
      logger.error('Failed to update team selection order', { teamId, gameworldId, error });
      throw error;
    }
  }
  constructor(private readonly dynamoDb: DynamoDbService) {}

  deleteAvailableTeam(team: AvailableTeam): Promise<number> {
    throw new Error('Method not implemented.');
  }

  batchInsertAvailableTeams(teams: AvailableTeam[]): Promise<void> {
    throw new Error('Method not implemented.');
  }

  async batchInsertTeams(teams: Team[]): Promise<void> {
    try {
      if (!process.env.TEAMS_TABLE_NAME) {
        throw new Error('TEAMS_TABLE_NAME environment variable is not defined');
      }

      logger.info('Starting batch insert of teams', { count: teams.length });
      const result = await this.dynamoDb.batchInsert<Team>(process.env.TEAMS_TABLE_NAME, teams);

      logger.info('Batch insert teams completed', {
        totalTeams: teams.length,
        successCount: result.successful.length,
        failureCount: result.failed.length,
      });

      return;
    } catch (error) {
      logger.error('Failed to batch insert teams:', { error });

      // If we have a catastrophic error, return all teams as failed
      return;
    }
  }

  async getTeamsByGameworld(gameworldId: string, includePlayers: boolean = false): Promise<Team[]> {
    const result = await this.dynamoDb.query<Team>(process.env.TEAMS_TABLE_NAME!, {
      hashKey: {
        name: 'gameworldId',
        value: gameworldId,
      },
    });
    return result.items || [];
  }

  async getTeamsByLeague(leagueId: string, includePlayers: boolean = false): Promise<Team[]> {
    const result = await this.dynamoDb.query<Team>(
      process.env.TEAMS_TABLE_NAME!,
      {
        hashKey: {
          name: 'leagueId',
          value: leagueId,
        },
      },
      'leagueIndex'
    );
    return result.items || [];
  }

  async updateTeamLeague(teamId: string, gameworldId: string, newLeagueId: string): Promise<void> {
    await this.dynamoDb.update<Team>(
      process.env.TEAMS_TABLE_NAME!,
      {
        teamId,
        gameworldId,
      },
      {
        league: Reference.createFromPK(League, newLeagueId),
        points: 0,
        goalsFor: 0,
        goalsAgainst: 0,
        wins: 0,
        draws: 0,
        losses: 0,
        played: 0,
      }
    );
  }

  async resetTeamStandings(teamId: string, gameworldId: string): Promise<void> {
    await this.dynamoDb.update<Team>(
      process.env.TEAMS_TABLE_NAME!,
      {
        teamId,
        gameworldId,
      },
      {
        points: 0,
        goalsFor: 0,
        goalsAgainst: 0,
        wins: 0,
        draws: 0,
        losses: 0,
        played: 0,
      }
    );
  }

  async updateTeamLeagues(
    teams: Team[],
    movements: TeamMovement[],
    gameworldId: string
  ): Promise<void> {
    const updates = movements.map((movement) =>
      this.dynamoDb.update<Team>(
        process.env.TEAMS_TABLE_NAME!,
        {
          teamId: movement.teamId,
          gameworldId: gameworldId,
        },
        {
          leagueId: movement.toLeagueId,
          points: 0,
          goalsFor: 0,
          goalsAgainst: 0,
          wins: 0,
          draws: 0,
          losses: 0,
          played: 0,
        }
      )
    );

    // For teams not being moved, just reset their standings
    const stationaryTeams = teams.filter(
      (team) => !movements.some((movement) => movement.teamId === team.teamId)
    );

    const stationaryUpdates = stationaryTeams.map((team) =>
      this.dynamoDb.update<Team>(
        process.env.TEAMS_TABLE_NAME!,
        {
          teamId: team.teamId,
          gameworldId: gameworldId,
        },
        {
          points: 0,
          goalsFor: 0,
          goalsAgainst: 0,
          wins: 0,
          draws: 0,
          losses: 0,
          played: 0,
        }
      )
    );

    await Promise.all([...updates, ...stationaryUpdates]);
  }

  async getRandomAvailableTeam(): Promise<AvailableTeam | null> {
    const { items } = await this.dynamoDb.scan<AvailableTeam>(
      process.env.AVAILABLE_TEAMS_TABLE_NAME!,
      {
        limit: 1,
      }
    );

    return items?.[0] || null;
  }
}
