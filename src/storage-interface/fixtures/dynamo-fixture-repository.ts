import { Fixture } from '@/entities/Fixture.js';
import { MatchEvent, MatchStats } from '@/model/fixture.js';
import DynamoDbService from '@/services/database/dynamo/dynamo-db-service.js';
import { Loaded } from '@mikro-orm/core';
import { FixtureRepository } from './fixture-repository.interface.js';

export class DynamoFixtureRepository implements FixtureRepository {
  getAllUnplayedFixtures(gameworldId?: string, leagueId?: string): Promise<Fixture[]> {
    return Promise.resolve([]);
  }
  constructor(private readonly dynamoDb: DynamoDbService) {}

  async batchInsertFixtures(fixtures: Fixture[]): Promise<void> {
    // Convert Fixture entities to the format expected by DynamoDB
    const dbFixtures = fixtures.map((fixture) => ({
      gameworldId_leagueId: `${fixture.gameworldId}#${fixture.league.id}`,
      gameworldId: fixture.gameworldId,
      leagueId: fixture.league.id,
      fixtureId: fixture.fixtureId,
      homeTeamId: fixture.homeTeam.teamId,
      homeTeamName: fixture.homeTeamName,
      awayTeamId: fixture.awayTeam.teamId,
      awayTeamName: fixture.awayTeamName,
      date: fixture.date,
      stats: fixture.stats,
      events: fixture.events,
      played: fixture.played,
      simulatedAt: fixture.simulatedAt,
    }));

    await this.dynamoDb.batchInsert(process.env.FIXTURES_TABLE_NAME!, dbFixtures);
  }

  async getFixture(
    gameworldId: string,
    leagueId: string,
    fixtureId: string
  ): Promise<Fixture | null> {
    const result = await this.dynamoDb.get(process.env.FIXTURES_TABLE_NAME!, {
      gameworldId_leagueId: `${gameworldId}#${leagueId}`,
      fixtureId: fixtureId,
    });

    if (!result) {
      return null;
    }

    // Convert DynamoDB item to Fixture entity
    // Note: This is a simplified conversion. In a real implementation,
    // you would need to properly handle the relationships.
    return this.mapDbItemToFixture(result);
  }

  async getFixturesByLeague(gameworldId: string, leagueId: string): Promise<Fixture[]> {
    const result = await this.dynamoDb.query(process.env.FIXTURES_TABLE_NAME!, {
      hashKey: {
        name: 'gameworldId_leagueId',
        value: `${gameworldId}#${leagueId}`,
      },
    });

    if (!result || !result.items || result.items.length === 0) {
      return [];
    }

    // Convert DynamoDB items to Fixture entities
    return result.items.map((item) => this.mapDbItemToFixture(item));
  }

  async getFixturesByTeam(
    gameworldId: string,
    teamId: string
  ): Promise<Loaded<Fixture, 'homeTeam' | 'awayTeam'>[]> {
    // For DynamoDB, we need to scan the table and filter by team ID
    // This is not efficient, but it's a simple implementation
    const result = await this.dynamoDb.scan(process.env.FIXTURES_TABLE_NAME!, {
      filterExpression:
        '(#homeTeamId = :teamId OR #awayTeamId = :teamId) AND #gameworldId = :gameworldId',
      expressionAttributeNames: {
        '#homeTeamId': 'homeTeamId',
        '#awayTeamId': 'awayTeamId',
        '#gameworldId': 'gameworldId',
      },
      expressionAttributeValues: {
        ':teamId': teamId,
        ':gameworldId': gameworldId,
      },
    });

    if (!result || !result.items || result.items.length === 0) {
      return [];
    }

    // Convert DynamoDB items to Fixture entities
    return result.items.map((item) => this.mapDbItemToFixture(item));
  }

  async getDueFixtures(gameworldId?: string, leagueId?: string): Promise<Fixture[]> {
    const currentTime = Date.now();

    let filterExpression = '#date <= :now AND #played = :played';
    const expressionAttributeNames = {
      '#date': 'date',
      '#played': 'played',
    };
    const expressionAttributeValues = {
      ':now': currentTime,
      ':played': false,
    };

    // Add gameworld filter if provided
    if (gameworldId) {
      filterExpression += ' AND #gameworldId = :gameworldId';
      expressionAttributeNames['#gameworldId'] = 'gameworldId';
      expressionAttributeValues[':gameworldId'] = gameworldId;
    }

    // Add league filter if provided
    if (leagueId && gameworldId) {
      filterExpression += ' AND #gameworldId_leagueId = :gameworldId_leagueId';
      expressionAttributeNames['#gameworldId_leagueId'] = 'gameworldId_leagueId';
      expressionAttributeValues[':gameworldId_leagueId'] = `${gameworldId}#${leagueId}`;
    } else if (leagueId) {
      filterExpression += ' AND #leagueId = :leagueId';
      expressionAttributeNames['#leagueId'] = 'leagueId';
      expressionAttributeValues[':leagueId'] = leagueId;
    }

    const result = await this.dynamoDb.scan(process.env.FIXTURES_TABLE_NAME!, {
      filterExpression,
      expressionAttributeNames,
      expressionAttributeValues,
    });

    if (!result || !result.items || result.items.length === 0) {
      return [];
    }

    // Sort by date
    const sortedItems = result.items.sort((a, b) => a.date - b.date);

    // Convert DynamoDB items to Fixture entities
    return sortedItems.map((item) => this.mapDbItemToFixture(item));
  }

  async updateFixtureResult(
    gameworldId: string,
    leagueId: string,
    fixtureId: string,
    seed: number,
    stats: MatchStats,
    events: MatchEvent[]
  ): Promise<void> {
    await this.dynamoDb.update(
      process.env.FIXTURES_TABLE_NAME!,
      {
        gameworldId_leagueId: `${gameworldId}#${leagueId}`,
        fixtureId: fixtureId,
      },
      {
        stats,
        events,
        seed,
        played: true,
        simulatedAt: Date.now(),
      }
    );
  }

  /**
   * Helper method to map a DynamoDB item to a Fixture entity
   * @param item The DynamoDB item
   * @returns A Fixture entity
   */
  private mapDbItemToFixture(item: any): Fixture {
    // This is a simplified conversion. In a real implementation,
    // you would need to properly handle the relationships.
    const fixture = new Fixture();
    fixture.fixtureId = item.fixtureId;
    fixture.gameworldId = item.gameworldId;
    // Set league as a reference object with just the ID
    fixture.league = { id: item.leagueId } as any;
    // Set home team as a reference object with just the ID
    fixture.homeTeam = { teamId: item.homeTeamId } as any;
    fixture.homeTeamName = item.homeTeamName;
    // Set away team as a reference object with just the ID
    fixture.awayTeam = { teamId: item.awayTeamId } as any;
    fixture.awayTeamName = item.awayTeamName;
    fixture.date = item.date;
    fixture.stats = item.stats;
    fixture.events = item.events;
    fixture.played = item.played;
    fixture.simulatedAt = item.simulatedAt;

    return fixture;
  }
}
