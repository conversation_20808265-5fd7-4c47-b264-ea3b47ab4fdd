import { Fixture } from '@/entities/Fixture.js';
import { MatchEvent, MatchStats } from '@/model/fixture.js';
import { MikroOrmService } from '@/storage-interface/mikro-orm-service.js';
import { logger } from '@/utils/logger.js';
import { FilterQuery, Loaded } from '@mikro-orm/core';
import { FixtureRepository } from './fixture-repository.interface.js';

export class MikroOrmFixtureRepository implements FixtureRepository {
  constructor(private mikroOrmService: MikroOrmService) {}

  async batchInsertFixtures(fixtures: Fixture[]): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      for (const fixture of fixtures) {
        em.persist(fixture);
      }
      await em.flush();
    } catch (error) {
      logger.error('Failed to create fixtures:', { error });
      throw error;
    }
  }

  async getFixture(
    gameworldId: string,
    leagueId: string,
    fixtureId: string
  ): Promise<Fixture | null> {
    const em = this.mikroOrmService.getEntityManager();
    return em.findOne(
      Fixture,
      { gameworldId, league: { id: leagueId }, fixtureId },
      {
        populate: ['homeTeam', 'awayTeam'],
      }
    );
  }

  async getFixturesByLeague(gameworldId: string, leagueId: string): Promise<Fixture[]> {
    const em = this.mikroOrmService.getEntityManager();
    const where: FilterQuery<Fixture> = { gameworldId, league: { id: leagueId } };
    return em.find(Fixture, where);
  }

  async getFixturesByTeam(
    gameworldId: string,
    teamId: string
  ): Promise<Loaded<Fixture, 'homeTeam' | 'awayTeam'>[]> {
    const em = this.mikroOrmService.getEntityManager();
    const where: FilterQuery<Fixture> = {
      gameworldId,
      $or: [{ homeTeam: { teamId } }, { awayTeam: { teamId } }],
    };
    const fixtures = await em.find(Fixture, where, {
      orderBy: { date: 'ASC' },
      populate: ['homeTeam', 'awayTeam'],
    });

    return fixtures;
  }

  async getDueFixtures(gameworldId?: string, leagueId?: string): Promise<Fixture[]> {
    const em = this.mikroOrmService.getEntityManager();
    const currentTime = Date.now();

    const where: FilterQuery<Fixture> = {
      date: { $lte: currentTime },
      played: false,
    };

    if (gameworldId) {
      where.gameworldId = gameworldId;
    }

    if (leagueId) {
      where.league = { id: leagueId };
    }

    return em.find(Fixture, where, {
      orderBy: { date: 'ASC' },
      populate: ['homeTeam', 'awayTeam'],
    });
  }

  async getAllUnplayedFixtures(gameworldId?: string, leagueId?: string): Promise<Fixture[]> {
    const em = this.mikroOrmService.getEntityManager();

    const where: FilterQuery<Fixture> = {
      played: false,
    };

    if (gameworldId) {
      where.gameworldId = gameworldId;
    }

    if (leagueId) {
      where.league = { id: leagueId };
    }

    return em.find(Fixture, where, {
      orderBy: { date: 'ASC' },
      populate: ['homeTeam', 'awayTeam'],
    });
  }

  async updateFixtureResult(
    gameworldId: string,
    leagueId: string,
    fixtureId: string,
    seed: number,
    stats: MatchStats,
    events: MatchEvent[]
  ): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    const fixture = await em.findOne(Fixture, {
      gameworldId,
      league: { id: leagueId },
      fixtureId,
    });

    if (!fixture) {
      throw new Error(`Fixture not found: ${gameworldId}, ${leagueId}, ${fixtureId}`);
    }

    fixture.stats = stats;
    fixture.events = events;
    fixture.played = true;
    fixture.seed = seed;
    fixture.simulatedAt = Date.now();

    await em.persistAndFlush(fixture);
  }
}
