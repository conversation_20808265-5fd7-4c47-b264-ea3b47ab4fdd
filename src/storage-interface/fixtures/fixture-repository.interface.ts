import { Fixture } from '@/entities/Fixture.js';
import { MatchEvent, MatchStats } from '@/model/fixture.js';
import { Loaded } from '@mikro-orm/core';

export interface FixtureRepository {
  /**
   * Batch insert fixtures
   * @param fixtures The fixtures to insert
   */
  batchInsertFixtures(fixtures: Fixture[]): Promise<void>;

  /**
   * Get a fixture by its ID
   * @param gameworldId The gameworld ID
   * @param leagueId The league ID
   * @param fixtureId The fixture ID
   */
  getFixture(gameworldId: string, leagueId: string, fixtureId: string): Promise<Fixture | null>;

  /**
   * Get fixtures by league
   * @param gameworldId The gameworld ID
   * @param leagueId The league ID
   */
  getFixturesByLeague(gameworldId: string, leagueId: string): Promise<Fixture[]>;

  /**
   * Get fixtures by team
   * @param gameworldId The gameworld ID
   * @param teamId The team ID
   */
  getFixturesByTeam(
    gameworldId: string,
    teamId: string
  ): Promise<Loaded<Fixture, 'homeTeam' | 'awayTeam'>[]>;

  /**
   * Get unplayed fixtures with dates in the past
   * @param gameworldId Optional gameworld ID to filter by
   * @param leagueId Optional league ID to filter by
   */
  getDueFixtures(gameworldId?: string, leagueId?: string): Promise<Fixture[]>;

  getAllUnplayedFixtures(gameworldId?: string, leagueId?: string): Promise<Fixture[]>;

  /**
   * Update fixture stats and mark as played
   * @param gameworldId The gameworld ID
   * @param leagueId The league ID
   * @param fixtureId The fixture ID
   * @param seed The seed used for the simulation
   * @param stats The match stats
   * @param events The match events
   */
  updateFixtureResult(
    gameworldId: string,
    leagueId: string,
    fixtureId: string,
    seed: number,
    stats: MatchStats,
    events: MatchEvent[]
  ): Promise<void>;
}
