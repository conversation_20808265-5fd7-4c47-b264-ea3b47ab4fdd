import { Player } from '@/entities/Player.js';
import { PlayerMatchHistory } from '@/entities/PlayerMatchHistory.js';
import { ScoutedPlayer } from '@/entities/ScoutedPlayer.js';
import { TransferListedPlayer } from '@/entities/TransferListedPlayer.js';
import { ScoutedPlayersRecord } from '@/model/scouted-player.js';
import DynamoDbService from '@/services/database/dynamo/dynamo-db-service.js';
import { QueryResult } from '@/services/database/shared/database-service.js';
import { PlayerRepository } from '@/storage-interface/players/player-repository.interface.js';
import { logger } from '@/utils/logger.js';
import { seededRandom } from '@/utils/seeded-random.js';
import { Loaded } from '@mikro-orm/core';

export class DynamoPlayerRepository implements PlayerRepository {
  private dynamoDb: DynamoDbService;

  batchCreateTransferListedPlayers(transferListedPlayers: TransferListedPlayer[]): Promise<void> {
    return Promise.resolve(undefined);
  }
  constructor(dynamoDb: DynamoDbService) {
    this.dynamoDb = dynamoDb;
  }

  batchCreatePlayers(players: Player[]): Promise<void> {
    throw new Error('Method not implemented.');
  }
  getPlayer(gameworldId: string, playerId: string): Promise<Player | null> {
    throw new Error('Method not implemented.');
  }
  getPlayersByTeam(gameworldId: string, teamId: string): Promise<Player[]> {
    throw new Error('Method not implemented.');
  }
  getPlayersByLeague(gameworldId: string, leagueId: string): Promise<Player[]> {
    throw new Error('Method not implemented.');
  }
  getPlayersWithoutTeam(gameworldId: string): Promise<Player[]> {
    throw new Error('Method not implemented.');
  }
  updatePlayer(player: Player): Promise<void> {
    throw new Error('Method not implemented.');
  }
  updatePlayerStats(updatedPlayers: Player[]): Promise<void> {
    throw new Error('Method not implemented.');
  }
  assignPlayerToTeam(gameworldId: string, playerId: string, teamId: string): Promise<void> {
    throw new Error('Method not implemented.');
  }
  removePlayerFromTeam(gameworldId: string, playerId: string): Promise<void> {
    throw new Error('Method not implemented.');
  }
  addPlayerMatchHistory(
    gameworldId: string,
    playerId: string,
    fixtureId: string,
    stats: PlayerMatchHistory
  ): Promise<void> {
    throw new Error('Method not implemented.');
  }
  getPlayerMatchHistory(
    gameworldId: string,
    playerId: string
  ): Promise<Array<{ fixtureId: string; stats: PlayerMatchHistory }>> {
    throw new Error('Method not implemented.');
  }

  /**
   * Check if a player has been scouted by a specific team
   * @param gameworldId The gameworld ID
   * @param playerId The player ID
   * @param teamId The team ID
   * @returns True if the player has been scouted by the team, false otherwise
   */
  async isPlayerScoutedByTeam(
    gameworldId: string,
    playerId: string,
    teamId: string
  ): Promise<boolean> {
    try {
      // Get the scouted players record for the team
      const scoutedPlayersRecord = await this.dynamoDb.get<ScoutedPlayersRecord>(
        process.env.SCOUTED_PLAYERS_TABLE_NAME!,
        {
          gameworldId,
          teamId,
        }
      );

      // If no record exists or it's empty, the player hasn't been scouted
      if (!scoutedPlayersRecord || !scoutedPlayersRecord.scoutedPlayers) {
        return false;
      }

      // Check if the player is in the scouted players list
      return scoutedPlayersRecord.scoutedPlayers.some((player) => player.playerId === playerId);
    } catch (error) {
      console.error('Error checking if player is scouted by team', {
        gameworldId,
        playerId,
        teamId,
        error,
      });
      throw error;
    }
  }

  /**
   * Get all players scouted by a specific team
   * @param gameworldId The gameworld ID
   * @param teamId The team ID
   * @param limit
   * @param lastEvaluatedKey
   * @returns Array of scouted players
   */
  async getPlayersScoutedByTeam(
    gameworldId: string,
    teamId: string,
    limit?: number,
    lastEvaluatedKey?: string
  ): Promise<Loaded<ScoutedPlayer, 'player' | 'player.attributes'>[]> {
    try {
      // Get the scouted players record for the team
      const scoutedPlayersRecord = await this.dynamoDb.get<ScoutedPlayersRecord>(
        process.env.SCOUTED_PLAYERS_TABLE_NAME!,
        {
          gameworldId,
          teamId,
        }
      );

      // If no record exists or it's empty, return an empty array
      if (!scoutedPlayersRecord || !scoutedPlayersRecord.scoutedPlayers) {
        return [];
      }

      // Convert the DynamoDB records to ScoutedPlayer entities
      // Note: This is a simplified conversion as DynamoDB doesn't store entities directly
      // In a real implementation, you might need to fetch the actual Player and Team entities
      const scoutedPlayers: ScoutedPlayer[] = scoutedPlayersRecord.scoutedPlayers.map((record) => {
        const scoutedPlayer = new ScoutedPlayer();
        scoutedPlayer.gameworldId = gameworldId;
        // Note: In a real implementation, you would set the actual references to Player and Team entities
        // This is just a placeholder implementation
        scoutedPlayer.scoutedAt = record.timestamp;
        return scoutedPlayer;
      });

      return scoutedPlayers;
    } catch (error) {
      logger.error('Error getting players scouted by team', { gameworldId, teamId, error });
      throw error;
    }
  }

  /**
   * Get random players from a league
   * @param gameworldId The gameworld ID
   * @param leagueId The league ID
   * @param count The number of random players to return
   * @returns Array of random players from the league
   */
  async getRandomPlayersFromLeague(
    gameworldId: string,
    leagueId: string,
    count: number
  ): Promise<Player[]> {
    try {
      // Get all players in the league
      const allPlayers = await this.getAllPlayersInLeague(gameworldId, leagueId);

      // Shuffle the players using seededRandom and return the requested count
      const shuffled = [...allPlayers].sort(() => seededRandom());
      return shuffled.slice(0, count);
    } catch (error) {
      logger.error('Error getting random players from league', {
        gameworldId,
        leagueId,
        count,
        error,
      });
      throw error;
    }
  }

  /**
   * Helper method to get all players in a league
   * @param gameworldId The gameworld ID
   * @param leagueId The league ID
   * @returns Array of all players in the league
   */
  private async getAllPlayersInLeague(gameworldId: string, leagueId: string): Promise<Player[]> {
    if (!process.env.PLAYERS_TABLE_NAME) {
      throw new Error('PLAYERS_TABLE_NAME environment variable is not set');
    }

    const allPlayers: Player[] = [];
    let lastEvaluatedKey: Record<string, any> | undefined = undefined;

    do {
      const result = (await this.dynamoDb.query<Player>(
        process.env.PLAYERS_TABLE_NAME,
        {
          hashKey: {
            name: 'leagueId',
            value: leagueId,
          },
          rangeKey: {
            name: 'gameworldId',
            value: gameworldId,
          },
        },
        'leagueIndex',
        {
          projection: ['playerId'],
          exclusiveStartKey: lastEvaluatedKey,
        }
      )) as QueryResult<Player>;

      if (result?.items) {
        allPlayers.push(...result.items);
      }

      lastEvaluatedKey = result?.lastEvaluatedKey;
    } while (lastEvaluatedKey);

    return allPlayers;
  }
}
