import { MikroORM } from '@mikro-orm/core';
import { PostgreSqlDriver } from '@mikro-orm/postgresql';

// Mock implementation of MikroOrmService
export class MikroOrmService {
  private initialized = false;

  constructor(private readonly orm$: Promise<MikroORM<PostgreSqlDriver>>) {}

  async initialize(): Promise<void> {
    this.initialized = true;
  }

  isInitialized(): boolean {
    return this.initialized;
  }

  getRepository(): any {
    return {};
  }
}

// Mock implementation of LeagueRepository
export class LeagueRepository {
  async findById(): Promise<any> {
    return {};
  }
}

// Mock implementation of TeamRepository
export class TeamRepository {
  async findById(): Promise<any> {
    return {};
  }
}

// Singleton instance of MikroOrmService
let mikroOrmServiceInstance: MikroOrmService | null = null;
let isInitializing = false;
let initializationPromise: Promise<void> | null = null;

/**
 * Initializes the database connection if it hasn't been initialized yet
 * @returns A promise that resolves when the database is initialized
 */
export async function initializeDatabase(): Promise<void> {
  // If already initialized, return immediately
  if (mikroOrmServiceInstance?.isInitialized()) {
    return Promise.resolve();
  }

  // If initialization is in progress, return the existing promise
  if (isInitializing && initializationPromise) {
    return initializationPromise;
  }

  // Start initialization
  isInitializing = true;
  initializationPromise = new Promise<void>((resolve, reject) => {
    const dbType = process.env.DATABASE_TYPE || 'dynamodb';

    if (dbType.toLowerCase() === 'postgres') {
      MikroORM.init<PostgreSqlDriver>({})
        .then(orm => {
          mikroOrmServiceInstance = new MikroOrmService(Promise.resolve(orm));
          return mikroOrmServiceInstance.initialize();
        })
        .then(() => {
          isInitializing = false;
          resolve();
        })
        .catch(error => {
          isInitializing = false;
          reject(error);
        });
    } else {
      // For DynamoDB, no initialization needed
      isInitializing = false;
      resolve();
    }
  });

  return initializationPromise;
}

/**
 * Gets the MikroOrmService instance, initializing it if necessary
 * @returns The MikroOrmService instance
 */
export async function getMikroOrmService(): Promise<MikroOrmService> {
  await initializeDatabase();

  if (!mikroOrmServiceInstance) {
    const orm = await MikroORM.init<PostgreSqlDriver>({});
    mikroOrmServiceInstance = new MikroOrmService(Promise.resolve(orm));
    await mikroOrmServiceInstance.initialize();
  }

  return mikroOrmServiceInstance;
}

/**
 * Gets a LeagueRepository instance
 * @returns A promise that resolves to a LeagueRepository
 */
export async function getLeagueRepository(): Promise<LeagueRepository> {
  return new LeagueRepository();
}

/**
 * Gets a TeamRepository instance
 * @returns A promise that resolves to a TeamRepository
 */
export async function getTeamRepository(): Promise<TeamRepository> {
  return new TeamRepository();
}
