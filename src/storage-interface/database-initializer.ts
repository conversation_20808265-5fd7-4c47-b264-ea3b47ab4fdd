import DynamoDbService from '@/services/database/dynamo/dynamo-db-service.js';
import { logger } from '@/utils/logger.js';
import { MikroORM } from '@mikro-orm/core';
import { PostgreSqlDriver } from '@mikro-orm/postgresql';

import { ManagerRepository } from '@/storage-interface/managers/manager-repository.js';
import { DynamoManagerRepository } from '@/storage-interface/managers/dynamo-manager-repository.js';
import { MikroOrmManagerRepository } from '@/storage-interface/managers/mikro-orm-manager-repository.js';
import { PlayerRepository } from '@/storage-interface/players/index.js';
import { MikroOrmPlayerRepository } from '@/storage-interface/players/mikro-orm-player-repository.js';
import { ScoutingRequestRepository } from '@/storage-interface/scouting-requests/index.js';
import { MikroOrmScoutingRequestRepository } from '@/storage-interface/scouting-requests/mikro-orm-scouting-request-repository.js';
import mikroOrmConfig from '../../mikro-orm.config.js';
import { DynamoFixtureRepository } from './fixtures/dynamo-fixture-repository.js';
import { FixtureRepository } from './fixtures/fixture-repository.interface.js';
import { MikroOrmFixtureRepository } from './fixtures/mikro-orm-fixture-repository.js';
import { DynamoLeagueRepository } from './leagues/dynamo-league-repository.js';
import { LeagueRepository } from './leagues/league-repository.interface.js';
import { MikroOrmLeagueRepository } from './leagues/mikro-orm-league-repository.js';
import { MikroOrmService } from './mikro-orm-service.js';
import { DynamoPlayerRepository } from './players/dynamo-player-repository.ts';
import { DynamoScoutingRepository } from './scouting/dynamo-scouting-repository.js';
import { MikroOrmScoutingRepository } from './scouting/mikro-orm-scouting-repository.js';
import { ScoutingRepository } from './scouting/scouting-repository.interface.js';
import { DynamoTeamRepository } from './teams/dynamo-team-repository.js';
import { MikroOrmTeamRepository } from './teams/mikro-orm-team-repository.js';
import { TeamRepository } from './teams/team-repository.interface.js';
import { DynamoGameworldRepository } from './gameworld/dynamo-gameworld-repository.js';
import { GameworldRepository } from './gameworld/gameworld-repository.interface.js';
import { MikroOrmGameworldRepository } from './gameworld/mikro-orm-gameworld-repository.js';
import { TransferRepository } from './transfers/transfer-repository.interface.js';
import { MikroOrmTransferRepository } from './transfers/mikro-orm-transfer-repository.js';
import { InboxRepository } from './inbox/inbox-repository.interface.js';
import { MikroOrmInboxRepository } from './inbox/mikro-orm-inbox-repository.js';

// Singleton instance of MikroOrmService
let mikroOrmServiceInstance: MikroOrmService | null = null;
let isInitializing = false;
let initializationPromise: Promise<void> | null = null;

/**
 * Initializes the database connection if it hasn't been initialized yet
 * @returns A promise that resolves when the database is initialized
 */
export async function initializeDatabase(): Promise<void> {
  // If already initialized, return immediately
  if (mikroOrmServiceInstance?.isInitialized()) {
    return Promise.resolve();
  }

  // If initialization is in progress, return the existing promise
  if (isInitializing && initializationPromise) {
    return initializationPromise;
  }

  // Start initialization
  isInitializing = true;
  initializationPromise = new Promise<void>((resolve, reject) => {
    const dbType = process.env.DATABASE_TYPE || 'dynamodb';

    if (dbType.toLowerCase() === 'postgres') {
      logger.info('Initializing MikroORM database connection');
      MikroORM.init<PostgreSqlDriver>(mikroOrmConfig)
        .then((orm) => {
          mikroOrmServiceInstance = new MikroOrmService(Promise.resolve(orm));
          return mikroOrmServiceInstance.initialize();
        })
        .then(() => {
          logger.info('MikroORM database connection initialized successfully');
          isInitializing = false;
          resolve();
        })
        .catch((error) => {
          isInitializing = false;
          logger.error('Failed to initialize database', { error });
          reject(error);
        });
    } else {
      // For DynamoDB, no initialization needed
      isInitializing = false;
      resolve();
    }
  });

  return initializationPromise;
}

/**
 * Gets the MikroOrmService instance, initializing it if necessary
 * @returns The MikroOrmService instance
 */
export async function getMikroOrmService(): Promise<MikroOrmService> {
  await initializeDatabase();

  if (!mikroOrmServiceInstance) {
    const orm = await MikroORM.init<PostgreSqlDriver>(mikroOrmConfig);
    mikroOrmServiceInstance = new MikroOrmService(Promise.resolve(orm));
    await mikroOrmServiceInstance.initialize();
  }

  return mikroOrmServiceInstance;
}

/**
 * Gets a LeagueRepository instance
 * @returns A promise that resolves to a LeagueRepository
 */
export async function getLeagueRepository(): Promise<LeagueRepository> {
  const dbType = process.env.DATABASE_TYPE || 'dynamodb';

  switch (dbType.toLowerCase()) {
    case 'postgres': {
      const mikroOrmService = await getMikroOrmService();
      return new MikroOrmLeagueRepository(mikroOrmService);
    }
    case 'dynamodb':
    default: {
      const dynamoDb = new DynamoDbService();
      return new DynamoLeagueRepository(dynamoDb);
    }
  }
}

/**
 * Gets a TeamRepository instance
 * @returns A promise that resolves to a TeamRepository
 */
export async function getTeamRepository(): Promise<TeamRepository> {
  const dbType = process.env.DATABASE_TYPE || 'dynamodb';

  switch (dbType.toLowerCase()) {
    case 'postgres': {
      const mikroOrmService = await getMikroOrmService();
      return new MikroOrmTeamRepository(mikroOrmService);
    }
    case 'dynamodb':
    default: {
      const dynamoDb = new DynamoDbService();
      return new DynamoTeamRepository(dynamoDb);
    }
  }
}

/**
 * Gets a FixtureRepository instance
 * @returns A promise that resolves to a FixtureRepository
 */
export async function getFixtureRepository(): Promise<FixtureRepository> {
  const dbType = process.env.DATABASE_TYPE || 'dynamodb';

  switch (dbType.toLowerCase()) {
    case 'postgres': {
      const mikroOrmService = await getMikroOrmService();
      return new MikroOrmFixtureRepository(mikroOrmService);
    }
    case 'dynamodb':
    default: {
      const dynamoDb = new DynamoDbService();
      return new DynamoFixtureRepository(dynamoDb);
    }
  }
}

/**
 * Gets a PlayerRepository instance
 * @returns A promise that resolves to a PlayerRepository
 */
export async function getPlayerRepository(): Promise<PlayerRepository> {
  const dbType = process.env.DATABASE_TYPE || 'dynamodb';

  switch (dbType.toLowerCase()) {
    case 'postgres': {
      const mikroOrmService = await getMikroOrmService();
      return new MikroOrmPlayerRepository(mikroOrmService);
    }
    case 'dynamodb':
    default: {
      const dynamoDb = new DynamoDbService();
      return new DynamoPlayerRepository(dynamoDb);
    }
  }
}

/**
 * Gets a ManagerRepository instance
 * @returns A promise that resolves to a ManagerRepository
 */
export async function getManagerRepository(): Promise<ManagerRepository> {
  const dbType = process.env.DATABASE_TYPE || 'dynamodb';

  switch (dbType.toLowerCase()) {
    case 'postgres': {
      const mikroOrmService = await getMikroOrmService();
      return new MikroOrmManagerRepository(mikroOrmService);
    }
    case 'dynamodb':
    default: {
      const dynamoDb = new DynamoDbService();
      return new DynamoManagerRepository(dynamoDb);
    }
  }
}

/**
 * Gets a ScoutingRepository instance
 * @returns A promise that resolves to a ScoutingRepository
 */
export async function getScoutingRepository(): Promise<ScoutingRepository> {
  const dbType = process.env.DATABASE_TYPE || 'dynamodb';
  const playerRepository = await getPlayerRepository();

  switch (dbType.toLowerCase()) {
    case 'postgres': {
      const mikroOrmService = await getMikroOrmService();
      return new MikroOrmScoutingRepository(mikroOrmService, playerRepository);
    }
    case 'dynamodb':
    default: {
      const dynamoDb = new DynamoDbService();
      return new DynamoScoutingRepository(dynamoDb, playerRepository);
    }
  }
}

/**
 * Gets a ScoutingRequestRepository instance
 * @returns A promise that resolves to a ScoutingRequestRepository
 */
export async function getScoutingRequestRepository(): Promise<ScoutingRequestRepository> {
  const dbType = process.env.DATABASE_TYPE || 'dynamodb';

  switch (dbType.toLowerCase()) {
    case 'postgres': {
      const mikroOrmService = await getMikroOrmService();
      return new MikroOrmScoutingRequestRepository(mikroOrmService);
    }
    case 'dynamodb':
    default: {
      // For now, we only support PostgreSQL for ScoutingRequest repository
      throw new Error('ScoutingRequest repository is only available with PostgreSQL');
    }
  }
}

/**
 * Gets a GameworldRepository instance
 * @returns A promise that resolves to a GameworldRepository
 */
export async function getGameworldRepository(): Promise<GameworldRepository> {
  const dbType = process.env.DATABASE_TYPE || 'dynamodb';

  switch (dbType.toLowerCase()) {
    case 'postgres': {
      const mikroOrmService = await getMikroOrmService();
      return new MikroOrmGameworldRepository(mikroOrmService);
    }
    case 'dynamodb':
    default: {
      const dynamoDb = new DynamoDbService();
      return new DynamoGameworldRepository(dynamoDb);
    }
  }
}

/**
 * Gets a TransferRepository instance
 * @returns A promise that resolves to a TransferRepository
 */
export async function getTransferRepository(): Promise<TransferRepository> {
  const dbType = process.env.DATABASE_TYPE || 'dynamodb';

  switch (dbType.toLowerCase()) {
    case 'postgres': {
      const mikroOrmService = await getMikroOrmService();
      return new MikroOrmTransferRepository(mikroOrmService);
    }
    case 'dynamodb':
    default: {
      // For now, we only support PostgreSQL for TransferRepository
      throw new Error('TransferRepository is only available with PostgreSQL');
    }
  }
}

/**
 * Gets an InboxRepository instance
 * @returns A promise that resolves to an InboxRepository
 */
export async function getInboxRepository(): Promise<InboxRepository> {
  const dbType = process.env.DATABASE_TYPE || 'dynamodb';

  switch (dbType.toLowerCase()) {
    case 'postgres': {
      const mikroOrmService = await getMikroOrmService();
      return new MikroOrmInboxRepository(mikroOrmService);
    }
    case 'dynamodb':
    default: {
      // For now, we only support PostgreSQL for InboxRepository
      throw new Error('InboxRepository is only available with PostgreSQL');
    }
  }
}
