import { ScoutingRequest, ScoutingRequestType } from '@/entities/ScoutingRequest.js';
import { Team } from '@/entities/Team.js';
import { MikroOrmService } from '@/storage-interface/mikro-orm-service.js';
import { ScoutingRequestRepository } from '@/storage-interface/scouting-requests/scouting-request-repository.interface.js';
import { logger } from '@/utils/logger.js';
import { Reference } from '@mikro-orm/core';

/**
 * MikroORM implementation of the ScoutingRequestRepository
 */
export class MikroOrmScoutingRequestRepository implements ScoutingRequestRepository {
  constructor(private mikroOrmService: MikroOrmService) {}

  /**
   * Create a new scouting request
   * @param gameworldId The gameworld ID
   * @param teamId The ID of the team making the request
   * @param type The type of scouting request
   * @param targetId The ID of the target (player, team, or league)
   * @param processAfter The time after which the request can be processed
   * @returns The created scouting request
   */
  async createScoutingRequest(
    gameworldId: string,
    teamId: string,
    type: ScoutingRequestType,
    targetId: string,
    processAfter: number
  ): Promise<ScoutingRequest> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // Create a new scouting request
      const scoutingRequest = new ScoutingRequest();
      scoutingRequest.gameworldId = gameworldId;
      scoutingRequest.team = Reference.createFromPK(Team, teamId);
      scoutingRequest.type = type;
      scoutingRequest.targetId = targetId;
      scoutingRequest.processAfter = processAfter;
      scoutingRequest.processed = false;

      // Save the scouting request
      em.persist(scoutingRequest);
      await em.flush();

      logger.debug('Created scouting request', {
        gameworldId,
        teamId,
        type,
        targetId,
        requestId: scoutingRequest.requestId,
      });

      return scoutingRequest;
    } catch (error) {
      logger.error('Error creating scouting request', {
        gameworldId,
        teamId,
        type,
        targetId,
        error,
      });
      throw error;
    }
  }

  /**
   * Get pending scouting requests
   * @param limit The maximum number of requests to return
   * @returns Array of pending scouting requests
   */
  async getPendingScoutingRequests(limit?: number): Promise<ScoutingRequest[]> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      const currentTime = Date.now();

      // Find all pending scouting requests that are ready to be processed
      return await em.find(
        ScoutingRequest,
        {
          processed: false,
          processAfter: { $lte: currentTime },
        },
        {
          orderBy: { processAfter: 'ASC' },
          limit: limit,
        }
      );
    } catch (error) {
      logger.error('Error getting pending scouting requests', { error });
      throw error;
    }
  }

  /**
   * Mark a scouting request as processed
   * @param requestId The ID of the request to mark as processed
   * @returns The updated scouting request
   */
  async markScoutingRequestAsProcessed(requestId: string): Promise<ScoutingRequest> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // Find the scouting request
      const scoutingRequest = await em.findOne(ScoutingRequest, { requestId });
      if (!scoutingRequest) {
        throw new Error(`Scouting request with ID ${requestId} not found`);
      }

      // Mark it as processed
      scoutingRequest.processed = true;
      scoutingRequest.processedAt = Date.now();

      // Save the changes
      await em.flush();

      logger.debug('Marked scouting request as processed', { requestId });

      return scoutingRequest;
    } catch (error) {
      logger.error('Error marking scouting request as processed', { requestId, error });
      throw error;
    }
  }

  /**
   * Get scouting requests by team
   * @param gameworldId The gameworld ID
   * @param teamId The ID of the team
   * @param limit The maximum number of requests to return
   * @returns Array of scouting requests for the team
   */
  async getScoutingRequestsByTeam(
    gameworldId: string,
    teamId: string,
    limit?: number
  ): Promise<ScoutingRequest[]> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // Find all scouting requests for the team
      return await em.find(
        ScoutingRequest,
        {
          gameworldId,
          team: teamId,
        },
        {
          orderBy: { createdAt: 'DESC' },
          limit: limit,
        }
      );
    } catch (error) {
      logger.error('Error getting scouting requests by team', {
        gameworldId,
        teamId,
        error,
      });
      throw error;
    }
  }
}
