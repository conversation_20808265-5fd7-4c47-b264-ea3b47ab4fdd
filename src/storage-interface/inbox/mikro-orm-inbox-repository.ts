import { Inbox } from '@/entities/Inbox.js';
import { MikroOrmService } from '@/storage-interface/mikro-orm-service.js';
import { DBInbox, InboxRepository } from '@/storage-interface/inbox/inbox-repository.interface.js';
import { logger } from '@/utils/logger.js';

/**
 * MikroORM implementation of the InboxRepository
 */
export class MikroOrmInboxRepository implements InboxRepository {
  constructor(private mikroOrmService: MikroOrmService) {}

  /**
   * Flush changes to the database
   */
  flush(): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    return em.flush();
  }

  /**
   * Create a new inbox message
   * @param date The date when the message was sent
   * @param message The message content
   * @param extra Additional information
   * @param flush Whether to flush changes to the database
   * @returns The created inbox message
   */
  async createMessage(
    date: number,
    message: string,
    extra: string,
    flush = false
  ): Promise<Inbox> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // Create a new inbox message
      const inboxMessage = new Inbox();
      inboxMessage.date = date;
      inboxMessage.message = message;
      inboxMessage.extra = extra;

      // Save the inbox message
      em.persist(inboxMessage);
      
      if (flush) {
        await em.flush();
      }

      return inboxMessage;
    } catch (error) {
      logger.error('Failed to create inbox message:', { error, date, message });
      throw error;
    }
  }

  /**
   * Get an inbox message by ID
   * @param messageId The ID of the message
   * @returns The inbox message, or null if not found
   */
  async getMessage(messageId: string): Promise<Inbox | null> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      return await em.findOne(Inbox, { id: messageId });
    } catch (error) {
      logger.error('Failed to get inbox message:', { error, messageId });
      throw error;
    }
  }

  /**
   * Get all inbox messages
   * @param limit Optional limit for pagination
   * @param offset Optional offset for pagination
   * @returns Array of inbox messages
   */
  async getAllMessages(
    limit?: number,
    offset?: number
  ): Promise<DBInbox[]> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      return await em.find(Inbox, {}, {
        limit,
        offset,
        orderBy: { date: 'DESC' }
      });
    } catch (error) {
      logger.error('Failed to get all inbox messages:', { error });
      throw error;
    }
  }

  /**
   * Update an inbox message
   * @param message The inbox message to update
   * @param flush Whether to flush changes to the database
   * @returns The updated inbox message
   */
  async updateMessage(
    message: Inbox,
    flush = false
  ): Promise<Inbox> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      em.persist(message);
      
      if (flush) {
        await em.flush();
      }

      return message;
    } catch (error) {
      logger.error('Failed to update inbox message:', { error, id: message.id });
      throw error;
    }
  }

  /**
   * Delete an inbox message
   * @param messageId The ID of the message to delete
   * @param flush Whether to flush changes to the database
   */
  async deleteMessage(
    messageId: string,
    flush = false
  ): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // Find the inbox message
      const inboxMessage = await em.findOne(Inbox, { id: messageId });
      if (!inboxMessage) {
        throw new Error(`Inbox message not found: ${messageId}`);
      }

      // Delete the inbox message
      em.remove(inboxMessage);
      
      if (flush) {
        await em.flush();
      }
    } catch (error) {
      logger.error('Failed to delete inbox message:', { error, messageId });
      throw error;
    }
  }
}