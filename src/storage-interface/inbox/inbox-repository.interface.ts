import { Inbox } from '@/entities/Inbox.js';
import { Loaded, PopulatePath } from '@mikro-orm/core';

export type DBInbox = Loaded<
  Inbox,
  never,
  PopulatePath.ALL,
  never
>;

/**
 * Repository for inbox messages
 */
export interface InboxRepository {
  /**
   * Create a new inbox message
   * @param date The date when the message was sent
   * @param message The message content
   * @param extra Additional information
   * @param flush Whether to flush changes to the database
   * @returns The created inbox message
   */
  createMessage(
    date: number,
    message: string,
    extra: string,
    flush?: boolean
  ): Promise<Inbox>;

  /**
   * Get an inbox message by ID
   * @param messageId The ID of the message
   * @returns The inbox message, or null if not found
   */
  getMessage(messageId: string): Promise<Inbox | null>;

  /**
   * Get all inbox messages
   * @param limit Optional limit for pagination
   * @param offset Optional offset for pagination
   * @returns Array of inbox messages
   */
  getAllMessages(
    limit?: number,
    offset?: number
  ): Promise<DBInbox[]>;

  /**
   * Update an inbox message
   * @param message The inbox message to update
   * @param flush Whether to flush changes to the database
   * @returns The updated inbox message
   */
  updateMessage(
    message: Inbox,
    flush?: boolean
  ): Promise<Inbox>;

  /**
   * Delete an inbox message
   * @param messageId The ID of the message to delete
   * @param flush Whether to flush changes to the database
   */
  deleteMessage(
    messageId: string,
    flush?: boolean
  ): Promise<void>;

  /**
   * Flush changes to the database
   */
  flush(): Promise<void>;
}