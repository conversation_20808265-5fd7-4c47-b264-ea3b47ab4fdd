import { League } from '@/entities/League.js';
import { Team } from '@/entities/Team.js';
import DynamoDbService from '@/services/database/dynamo/dynamo-db-service.js';
import { LeagueRepository } from './league-repository.interface.js';

export class DynamoLeagueRepository implements LeagueRepository {
  constructor(private readonly dynamoDb: DynamoDbService) {}

  async batchCreateLeagues(leagues: League[]): Promise<void> {
    // Direct mapping to single table
    await this.dynamoDb.batchInsert(process.env.LEAGUES_TABLE_NAME!, leagues);
  }

  async getLeague(
    gameworldId: string,
    leagueId: string,
    includeTeams: boolean = false
  ): Promise<League | null> {
    const league = await this.dynamoDb.get<League>(process.env.LEAGUES_TABLE_NAME!, {
      gameworldId,
      id: leagueId,
    });

    if (league && includeTeams) {
      const teamsResult = await this.dynamoDb.query<Team>(
        process.env.TEAMS_TABLE_NAME!,
        {
          hashKey: {
            name: 'leagueId',
            value: leagueId,
          },
          rangeKey: {
            name: 'gameworldId',
            value: gameworldId,
          },
        },
        'leagueIndex'
      );
      league.teams = teamsResult.items || [];
    }

    return league;
  }

  async getLeaguesByGameworld(
    gameworldId: string,
    includeTeams: boolean = false
  ): Promise<League[]> {
    const result = await this.dynamoDb.query<League>(process.env.LEAGUES_TABLE_NAME!, {
      hashKey: { name: 'gameworldId', value: gameworldId },
    });

    const leagues = result.items;

    // If teams are requested, fetch them for each league
    if (includeTeams && leagues.length > 0) {
      const teamPromises = leagues.map(async (league) => {
        const teamsResult = await this.dynamoDb.query<Team>(
          process.env.TEAMS_TABLE_NAME!,
          {
            hashKey: {
              name: 'leagueId',
              value: league.id,
            },
            rangeKey: {
              name: 'gameworldId',
              value: gameworldId,
            },
          },
          'leagueIndex'
        );
        league.teams = teamsResult.items || [];
        return league;
      });

      return Promise.all(teamPromises);
    }

    return leagues;
  }

  getLeagueHierarchy(gameworldId: string, rootLeagueId: string): Promise<League[]> {
    return Promise.resolve([]);
  }

  updateLeague(league: League): Promise<void> {
    return Promise.resolve(undefined);
  }
}
