import { League } from '@/entities/League.js';

export interface LeagueRepository {
  batchCreateLeagues(leagues: League[]): Promise<void>;

  getLeaguesByGameworld(gameworldId: string, includeTeams?: boolean): Promise<League[]>;

  getLeague(gameworldId: string, leagueId: string, includeTeams: boolean): Promise<League | null>;

  getLeagueHierarchy(gameworldId: string, rootLeagueId: string): Promise<League[]>;

  updateLeague(league: League): Promise<void>;

  // Add other methods as needed
}

export interface BatchWriteResult<T> {
  successful: T[];
  failed: Array<{ item: T; error: Error }>;
}
