import { Repositories } from '@/middleware/database/types.js';
import { jest } from '@jest/globals';

/**
 * Mock repositories for testing
 *
 * These mocks implement all methods from the repository interfaces
 * to ensure type safety and complete mocking for tests.
 */

/**
 * Mock implementation of TeamRepository
 */
export const mockTeamRepository = {
  batchInsertTeams: jest.fn(),
  batchInsertAvailableTeams: jest.fn(),
  getTeamsByGameworld: jest.fn(),
  getTeamsByLeague: jest.fn(),
  updateTeamLeague: jest.fn(),
  resetTeamStandings: jest.fn(),
  updateTeamLeagues: jest.fn(),
  getRandomAvailableTeam: jest.fn(),
  deleteAvailableTeam: jest.fn(),
  updateTeamStandings: jest.fn(),
  getTeam: jest.fn(),
  updateTeamBalance: jest.fn(),
  updateTeamSelectionOrder: jest.fn(),
};

/**
 * Mock implementation of ManagerRepository
 */
export const mockManagerRepository = {
  createManager: jest.fn(),
  getManagerById: jest.fn(),
  updateManager: jest.fn(),
  deleteManager: jest.fn(),
};

/**
 * Mock implementation of PlayerRepository
 */
export const mockPlayerRepository = {
  batchCreatePlayers: jest.fn(),
  getPlayer: jest.fn(),
  getPlayersByTeam: jest.fn(),
  getPlayersByLeague: jest.fn(),
  getPlayersWithoutTeam: jest.fn(),
  updatePlayer: jest.fn(),
  updatePlayerStats: jest.fn(),
  assignPlayerToTeam: jest.fn(),
  removePlayerFromTeam: jest.fn(),
  addPlayerMatchHistory: jest.fn(),
  getPlayerMatchHistory: jest.fn(),
  batchCreateTransferListedPlayers: jest.fn(),
  getTransferListedPlayers: jest.fn(),
  isPlayerScoutedByTeam: jest.fn(),
  getPlayersScoutedByTeam: jest.fn(),
  getRandomPlayersFromLeague: jest.fn(),
};

/**
 * Mock implementation of LeagueRepository
 */
export const mockLeagueRepository = {
  batchCreateLeagues: jest.fn(),
  getLeaguesByGameworld: jest.fn(),
  getLeague: jest.fn(),
  getLeagueHierarchy: jest.fn(),
  updateLeague: jest.fn(),
};

/**
 * Mock implementation of FixtureRepository
 */
export const mockFixtureRepository = {
  batchInsertFixtures: jest.fn(),
  getFixture: jest.fn(),
  getFixturesByLeague: jest.fn(),
  getFixturesByTeam: jest.fn(),
  getDueFixtures: jest.fn(),
  updateFixtureResult: jest.fn(),
};

/**
 * Mock implementation of ScoutingRepository
 */
export const mockScoutingRepository = {
  scoutRandomPlayersFromLeague: jest.fn(),
  scoutPlayersFromTeam: jest.fn(),
  getPlayersScoutedByTeam: jest.fn(),
  saveScoutedPlayers: jest.fn(),
};

/**
 * Mock implementation of ScoutingRequestRepository
 */
export const mockScoutingRequestRepository = {
  createScoutingRequest: jest.fn(),
  getScoutingRequest: jest.fn(),
  updateScoutingRequest: jest.fn(),
  getPendingScoutingRequests: jest.fn(),
};

/**
 * Mock implementation of TransferRepository
 */
export const mockTransferRepository = {
  submitOffer: jest.fn(),
  getTransferRequest: jest.fn(),
  getTransferRequestsByBuyer: jest.fn(),
  getTransferRequestsBySeller: jest.fn(),
  getTransferRequestsByPlayer: jest.fn(),
  submitCounterOffer: jest.fn(),
  deleteTransferRequest: jest.fn(),
};

/**
 * Mock implementation of GameworldRepository
 */
export const mockGameworldRepository = {
  getGameworld: jest.fn(),
  getAllGameworlds: jest.fn(),
  getCompletedSeasons: jest.fn(),
  createGameworld: jest.fn(),
  updateGameworld: jest.fn(),
  updateGameworldEndDate: jest.fn(),
};

/**
 * Creates a complete mock repositories object that can be used in tests
 * @returns A repositories object with all repository mocks
 */
export const createMockRepositories = (): Repositories => ({
  leagueRepository: mockLeagueRepository,
  teamRepository: mockTeamRepository,
  fixtureRepository: mockFixtureRepository,
  playerRepository: mockPlayerRepository,
  managerRepository: mockManagerRepository,
  scoutingRepository: mockScoutingRepository,
  scoutingRequestRepository: mockScoutingRequestRepository,
  transferRepository: mockTransferRepository,
  gameworldRepository: mockGameworldRepository,
});

/**
 * Resets all mock repositories
 * Useful for cleaning up between tests
 */
export const resetAllRepositoryMocks = (): void => {
  // Reset all mocks from each repository
  Object.values(mockLeagueRepository).forEach((mock) => mock.mockReset());
  Object.values(mockTeamRepository).forEach((mock) => mock.mockReset());
  Object.values(mockFixtureRepository).forEach((mock) => mock.mockReset());
  Object.values(mockPlayerRepository).forEach((mock) => mock.mockReset());
  Object.values(mockManagerRepository).forEach((mock) => mock.mockReset());
  Object.values(mockScoutingRepository).forEach((mock) => mock.mockReset());
  Object.values(mockScoutingRequestRepository).forEach((mock) => mock.mockReset());
  Object.values(mockTransferRepository).forEach((mock) => mock.mockReset());
  Object.values(mockGameworldRepository).forEach((mock) => mock.mockReset());
};
