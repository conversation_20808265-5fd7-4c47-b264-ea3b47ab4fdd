import { League } from '@/entities/League.js';
import { Team } from '@/entities/Team.js';
import { Reference } from '@mikro-orm/core';
import { v4 as uuidv4 } from 'uuid';

/**
 * Creates a mock Team entity for testing
 */
export function createMockTeamEntity(attributes: Partial<Team> = {}): Team {
  const team = new Team();

  // Set required properties with defaults
  team.teamId = attributes.teamId ?? uuidv4();
  team.gameworldId = attributes.gameworldId ?? 'test-gameworld';
  team.tier = attributes.tier ?? 1;
  team.teamName = attributes.teamName ?? 'Test Team';

  // Set league reference
  if (attributes.league) {
    team.league = attributes.league;
  } else {
    const leagueId = attributes.league?.id ?? 'test-league';
    team.league = Reference.createFromPK(League, leagueId);
  }

  // Set optional properties with defaults
  team.balance = attributes.balance ?? 300000;
  team.played = attributes.played ?? 0;
  team.points = attributes.points ?? 0;
  team.goalsFor = attributes.goalsFor ?? 0;
  team.goalsAgainst = attributes.goalsAgainst ?? 0;
  team.wins = attributes.wins ?? 0;
  team.draws = attributes.draws ?? 0;
  team.losses = attributes.losses ?? 0;
  team.selectionOrder = attributes.selectionOrder ?? [];

  return team;
}

/**
 * Creates a mock League entity for testing
 */
export function createMockLeagueEntity(attributes: Partial<League> = {}): League {
  const league = new League();

  // Set required properties with defaults
  league.id = attributes.id ?? uuidv4();
  league.gameworldId = attributes.gameworldId ?? 'test-gameworld';
  league.name = attributes.name ?? 'Test League';
  league.tier = attributes.tier ?? 1;

  return league;
}
