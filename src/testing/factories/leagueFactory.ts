import { Factory } from 'interface-forge';
import { League } from '@/entities/League.js';
import { Ref } from '@mikro-orm/core';
import { Gameworld } from '@/entities/Gameworld.js';

export const LeagueFactory = new Factory<League>((factory: Factory<League>, iteration: number) => {
  return {
    id: factory.string.uuid(),
    gameworld: {
      id: factory.string.uuid(),
    } as Ref<Gameworld>,
    name: factory.string.alphanumeric({ length: 10 }),
    tier: factory.number.int({ min: 1, max: 10 }),
    parentLeague: null, // Can be set to another League reference if needed
    leagueChildren: [],
    leagueRules: null, // Can be set to a LeagueRules reference if needed
    teams: [],
  };
});
