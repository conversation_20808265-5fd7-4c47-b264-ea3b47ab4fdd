/* eslint-disable @typescript-eslint/no-unsafe-argument,@typescript-eslint/no-explicit-any */
import { handler } from '@/functions/transfers/respondToTransferOffers.js';
import { initializeDatabase } from '@/storage-interface/database-initializer.js';
import createHttpEvent from '@/testing/createHttpEvent.js';

process.env.DATABASE_TYPE = 'postgres';
process.env.DEBUG_USER_ID = '118bf5a0-7011-70ac-b498-efb221ba66be';

async function startTest() {
  await initializeDatabase();

  const mockEvent = createHttpEvent({
    pathParameters: {
      gameworldId: '3aa91af4-83e7-40e2-9297-203288058b64',
    },
  });
  /*const result = await (await getTransferRepository()).getCompletedAuctions();
  logger.debug('result', { result });
  return;*/
  await handler(mockEvent, {} as any);
}

startTest();
