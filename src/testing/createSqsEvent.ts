import type { SQSEvent } from 'aws-lambda';

import createEvent from '@/testing/aws-mocks/index.js';
import type { PartialSqsRecord } from './types.js';

export default function createSqsEvent(records: PartialSqsRecord[] = []): SQSEvent {
  const defaultEvent = createEvent('aws:sqs', {});
  const defaultRecord = defaultEvent.Records[0]!;
  return {
    Records: records.map((record) => ({
      ...defaultRecord,
      ...record,
      attributes: {
        ...defaultRecord.attributes,
        ...record.attributes,
      },
      messageAttributes: {
        ...defaultRecord.messageAttributes,
        ...record.messageAttributes,
      },
      body: record.body ? JSON.stringify(record.body) : defaultRecord.body,
    })),
  };
}
