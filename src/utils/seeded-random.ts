import crypto from 'crypto';

const RANDOM_MODULUS = 2147483647; // Largest 32-bit prime number used for modulus
const RANDOM_MULTIPLIER = 16807; // Common multiplier in <PERSON><PERSON><PERSON>'s RNG
const RANDOM_NORMALIZER = RANDOM_MODULUS - 1; // Used to normalize between 0 and 1

export let seededRandom: () => number = () => 0;

/**
 * Generates a random integer between min (inclusive) and max (inclusive)
 * using the seeded random number generator.
 *
 * @param min - The minimum integer value (inclusive).
 * @param max - The maximum integer value (inclusive).
 * @returns A random integer between min and max.
 */
export function seededRandomIntInRange(min: number, max: number): number {
  return Math.floor(seededRandom() * (max - min + 1)) + min;
}

export function setAndReturnSeededRandom() {
  const seed = Number(crypto.randomBytes(8).readBigUInt64BE() % BigInt(Number.MAX_SAFE_INTEGER));
  setRandomSeed(seed);
  return seed;
}

// Generates a seeded random number generator function to ensure reproducible results
export function setRandomSeed(seed: number): void {
  let value = seed % RANDOM_MODULUS;
  seededRandom = function () {
    value = (value * RANDOM_MULTIPLIER) % RANDOM_MODULUS;
    return (value - 1) / RANDOM_NORMALIZER; // Normalizes to a float between 0 and 1
  };
}
