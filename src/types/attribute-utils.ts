import { PlayerAttributes } from '../entities/PlayerAttributes.js';

/**
 * Extracts the base attribute name from a property name with 'Current' or 'Potential' suffix
 */
type ExtractBaseAttributeName<T extends string> = T extends
  | `${infer Base}Current`
  | `${infer Base}Potential`
  ? Base
  : never;

/**
 * Gets all property keys from PlayerAttributes that end with 'Current' or 'Potential'
 */
type AttributeKeys = {
  [K in keyof PlayerAttributes]: K extends string
    ? K extends `${string}Current` | `${string}Potential`
      ? K
      : never
    : never;
}[keyof PlayerAttributes];

/**
 * Extracts all base attribute names from PlayerAttributes
 * (e.g., 'reflexes' from 'reflexesCurrent' and 'reflexesPotential')
 */
export type BaseAttributeName = ExtractBaseAttributeName<AttributeKeys>;

/**
 * Type guard to check if a string is a valid base attribute name
 */
export function isBaseAttributeName(key: string): key is BaseAttributeName {
  // Get all keys from PlayerAttributes
  const attributeKeys = Object.keys(new PlayerAttributes());

  // Check if the key + 'Current' exists in PlayerAttributes
  return attributeKeys.includes(`${key}Current`);
}

/**
 * Maps a base attribute name to its corresponding current and potential properties
 * in the PlayerAttributes class
 */
export type AttributeMapping<T extends BaseAttributeName> = {
  current: `${T}Current`;
  potential: `${T}Potential`;
};

/**
 * Gets the current and potential property names for a given base attribute name
 */
export function getAttributeProperties<T extends BaseAttributeName>(
  baseAttribute: T
): AttributeMapping<T> {
  // Ensure baseAttribute is not undefined
  if (!baseAttribute) {
    throw new Error('baseAttribute cannot be undefined');
  }

  return {
    current: `${baseAttribute}Current`,
    potential: `${baseAttribute}Potential`,
  };
}
