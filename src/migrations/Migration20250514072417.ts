import { Migration } from '@mikro-orm/migrations';

export class Migration20250514072417 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "transfer_list" drop constraint "transfer_list_player_player_id_foreign";`);

    this.addSql(`drop index "transfer_list_player_id_index";`);
    this.addSql(`alter table "transfer_list" drop column "player_id";`);

    this.addSql(`alter table "transfer_list" alter column "player_player_id" drop default;`);
    this.addSql(`alter table "transfer_list" alter column "player_player_id" type uuid using ("player_player_id"::text::uuid);`);
    this.addSql(`alter table "transfer_list" alter column "player_player_id" drop not null;`);
    this.addSql(`alter table "transfer_list" add constraint "transfer_list_player_player_id_foreign" foreign key ("player_player_id") references "players" ("player_id") on update cascade on delete cascade;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "transfer_list" drop constraint "transfer_list_player_player_id_foreign";`);

    this.addSql(`alter table "transfer_list" add column "player_id" uuid not null;`);
    this.addSql(`alter table "transfer_list" alter column "player_player_id" drop default;`);
    this.addSql(`alter table "transfer_list" alter column "player_player_id" type uuid using ("player_player_id"::text::uuid);`);
    this.addSql(`alter table "transfer_list" alter column "player_player_id" set not null;`);
    this.addSql(`alter table "transfer_list" add constraint "transfer_list_player_player_id_foreign" foreign key ("player_player_id") references "players" ("player_id") on update cascade;`);
    this.addSql(`create index "transfer_list_player_id_index" on "transfer_list" ("player_id");`);
  }

}
