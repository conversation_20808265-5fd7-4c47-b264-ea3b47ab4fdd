import { Migration } from '@mikro-orm/migrations';

export class Migration20250521202001 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "transfer_list" drop constraint "transfer_list_player_player_id_foreign";`);

    this.addSql(`alter table "league_rules" add column "maximum_prize" int not null default 0, add column "minimum_prize" int not null default 0;`);

    this.addSql(`alter table "transfer_list" alter column "player_player_id" drop default;`);
    this.addSql(`alter table "transfer_list" alter column "player_player_id" type uuid using ("player_player_id"::text::uuid);`);
    this.addSql(`alter table "transfer_list" alter column "player_player_id" set not null;`);
    this.addSql(`alter table "transfer_list" add constraint "transfer_list_player_player_id_foreign" foreign key ("player_player_id") references "players" ("player_id") on update cascade;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "transfer_list" drop constraint "transfer_list_player_player_id_foreign";`);

    this.addSql(`alter table "league_rules" drop column "maximum_prize", drop column "minimum_prize";`);

    this.addSql(`alter table "transfer_list" alter column "player_player_id" drop default;`);
    this.addSql(`alter table "transfer_list" alter column "player_player_id" type uuid using ("player_player_id"::text::uuid);`);
    this.addSql(`alter table "transfer_list" alter column "player_player_id" drop not null;`);
    this.addSql(`alter table "transfer_list" add constraint "transfer_list_player_player_id_foreign" foreign key ("player_player_id") references "players" ("player_id") on update cascade on delete cascade;`);
  }

}
