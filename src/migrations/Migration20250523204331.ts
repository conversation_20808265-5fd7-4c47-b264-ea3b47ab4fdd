import { Migration } from '@mikro-orm/migrations';

export class Migration20250523204331 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "league_rules" alter column "maximum_prize" type int using ("maximum_prize"::int);`);
    this.addSql(`alter table "league_rules" alter column "maximum_prize" set default 0;`);
    this.addSql(`alter table "league_rules" alter column "minimum_prize" type int using ("minimum_prize"::int);`);
    this.addSql(`alter table "league_rules" alter column "minimum_prize" set default 0;`);

    this.addSql(`alter table "manager" add column "push_token" varchar(255) null;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "league_rules" alter column "maximum_prize" drop default;`);
    this.addSql(`alter table "league_rules" alter column "maximum_prize" type int using ("maximum_prize"::int);`);
    this.addSql(`alter table "league_rules" alter column "minimum_prize" drop default;`);
    this.addSql(`alter table "league_rules" alter column "minimum_prize" type int using ("minimum_prize"::int);`);

    this.addSql(`alter table "manager" drop column "push_token";`);
  }

}
