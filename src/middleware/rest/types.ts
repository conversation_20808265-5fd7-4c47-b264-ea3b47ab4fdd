import { Repositories } from '@/middleware/database/types.js';
import { EventWithRepositories } from '@/middleware/event/types.js';
import type { MiddlewareObj as MiddyMiddlewareObj } from '@middy/core';
import Ajv, { type Options as AjvOptions } from 'ajv';
import type {
  APIGatewayProxyEvent,
  APIGatewayProxyResult,
  Handler as AWSHandler,
  Context,
} from 'aws-lambda';

export interface HttpEvent<TBody, TPathParameters, TQueryStringParameters>
  extends Omit<APIGatewayProxyEvent, 'body' | 'pathParameters' | 'queryStringParameters'>,
    EventWithRepositories {
  body: TBody;
  pathParameters: TPathParameters;
  queryStringParameters: TQueryStringParameters;
  multiValueQueryStringParameters: NonNullable<
    APIGatewayProxyEvent['multiValueQueryStringParameters']
  >;
  context: Context & {
    repositories: Repositories;
  };
}

export type HttpHandler<
  TBody = void,
  TPathParameters = void,
  TQueryStringParameters = void,
> = AWSHandler<HttpEvent<TBody, TPathParameters, TQueryStringParameters>, APIGatewayProxyResult>;

export type HttpMiddlewareObj = MiddyMiddlewareObj<APIGatewayProxyEvent, APIGatewayProxyResult>;

export interface HttpMiddifyOptions {
  schema?: object;
  validatorOptions?: AjvOptions;
  validatorCallback?: (ajv: Ajv) => void;
  injectRepositories?: boolean;
}
