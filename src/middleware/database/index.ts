import { Repositories } from '@/middleware/database/types.js';
import { EventWithRepositories } from '@/middleware/event/types.js';
import {
  getFixtureRepository,
  getGameworldRepository,
  getInboxRepository,
  getLeagueRepository,
  getManagerRepository,
  getMikroOrmService,
  getPlayerRepository,
  getScoutingRepository,
  getScoutingRequestRepository,
  getTeamRepository,
  getTransferRepository,
  initializeDatabase,
} from '@/storage-interface/database-initializer.js';
import { logger } from '@/utils/logger.js';
import { Request } from '@middy/core';

// We'll use the EventWithRepositories interface from event/types.ts

/**
 * Middleware that initializes the database connection and injects repository instances
 * into the event context.
 */
export const databaseMiddleware = () => {
  return {
    before: (request: Request<EventWithRepositories>): Promise<void> => {
      logger.debug('Initializing database connection');

      return initializeDatabase()
        .then(() => {
          // Create repositories and inject them into the context
          return Promise.all([
            getLeagueRepository(),
            getTeamRepository(),
            getFixtureRepository(),
            getPlayerRepository(),
            getManagerRepository(),
            getScoutingRepository(),
            getScoutingRequestRepository(),
            getGameworldRepository(),
            getTransferRepository(),
            getInboxRepository(),
            getMikroOrmService(),
          ]);
        })
        .then(
          ([
            leagueRepository,
            teamRepository,
            fixtureRepository,
            playerRepository,
            managerRepository,
            scoutingRepository,
            scoutingRequestRepository,
            gameworldRepository,
            transferRepository,
            inboxRepository,
          ]) => {
            const repositories: Repositories = {
              leagueRepository,
              teamRepository,
              fixtureRepository,
              playerRepository,
              managerRepository,
              scoutingRepository,
              scoutingRequestRepository,
              gameworldRepository,
              transferRepository,
              inboxRepository,
            };

            // Ensure context exists
            if (!request.event.context) {
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              request.event.context = {};
            }

            // Add repositories to context
            request.event.context.repositories = repositories;

            logger.debug('Database connection initialized and repositories injected');
          }
        )
        .catch((error) => {
          logger.error('Failed to initialize database connection', { error });
          // Create a more helpful error message
          const enhancedError = new Error(
            `Database initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`
          );
          if (error instanceof Error && error.stack) {
            enhancedError.stack = error.stack;
          }
          throw enhancedError;
        });
    },
  };
};
