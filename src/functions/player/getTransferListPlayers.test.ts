import { TransferListedPlayer } from '@/entities/TransferListedPlayer.js';
import createHttpEvent from '@/testing/createHttpEvent.js';
import { handler } from './getTransferListPlayers.js';
import { mockPlayerRepository, resetAllRepositoryMocks } from '@/testing/mockRepositories.js';
import { PlayerFactory } from "@/testing/factories/playerFactory.js";

describe('Get Transfer List Players Handler', () => {
  const mockContext = {} as any;
  let querySpy: jest.SpyInstance;

  function createMockPlayer(override: Partial<TransferListedPlayer> = {}): TransferListedPlayer {
    return {
      player: PlayerFactory.build({}),
      playerId: '',
      gameworldId: '',
      auctionStartPrice: 0,
      auctionCurrentPrice: 0,
      auctionEndTime: 0,
      bidHistory: { getItems: () => [] },
      ...override,
    };
  }

  beforeEach(() => {
    resetAllRepositoryMocks();

    process.env.TRANSFER_LISTED_PLAYERS_TABLE_NAME = 'transfer-listed-players-table';

    // Create mock players
    const mockPlayers: TransferListedPlayer[] = [
      createMockPlayer({ playerId: '1', gameworldId: 'test-gameworld' }),
      createMockPlayer({ playerId: '2', gameworldId: 'test-gameworld' }),
      createMockPlayer({ playerId: '3', gameworldId: 'test-gameworld' }),
    ];

    querySpy = jest.mocked(mockPlayerRepository.getTransferListedPlayers)
      .mockResolvedValue({ items: mockPlayers });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return players with default limit when no parameters provided', async () => {
    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld' },
    });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(200);
    expect(body.players).toHaveLength(3);
    expect(querySpy).toHaveBeenCalledWith(
      'transfer-listed-players-table',
      {
        hashKey: { name: 'gameworldId', value: 'test-gameworld' },
      },
      undefined,
      {
        limit: 25,
        exclusiveStartKey: undefined,
      }
    );
  });

  it('should respect custom limit parameter', async () => {
    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld' },
      queryStringParameters: { limit: '2' },
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(200);
    expect(querySpy).toHaveBeenCalledWith(
      'transfer-listed-players-table',
      {
        hashKey: { name: 'gameworldId', value: 'test-gameworld' },
      },
      undefined,
      {
        limit: 2,
        exclusiveStartKey: undefined,
      }
    );
  });

  it('should handle pagination with lastEvaluatedKey', async () => {
    const mockLastEvaluatedKey = { playerId: '3', gameworldId: 'test-gameworld' };
    querySpy.mockResolvedValueOnce({
      items: [createMockPlayer({ playerId: '4', gameworldId: 'test-gameworld' })],
      lastEvaluatedKey: mockLastEvaluatedKey,
    });

    const lastEvaluatedKeyBase64 = Buffer.from(
      JSON.stringify({
        playerId: '2',
        gameworldId: 'test-gameworld',
      })
    ).toString('base64');

    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld' },
      queryStringParameters: { lastEvaluatedKey: lastEvaluatedKeyBase64 },
    });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(200);
    expect(body.lastEvaluatedKey).toBe(
      Buffer.from(JSON.stringify(mockLastEvaluatedKey)).toString('base64')
    );
  });

  it('should handle invalid lastEvaluatedKey', async () => {
    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld' },
      queryStringParameters: { lastEvaluatedKey: 'invalid-base64' },
    });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(400);
    expect(body.error).toBe('Invalid lastEvaluatedKey');
  });

  it('should handle case when no players are found', async () => {
    querySpy.mockResolvedValueOnce({ items: [] });

    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld' },
    });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(404);
    expect(body.error).toBe('No players found');
  });

  it('should handle case when query returns undefined', async () => {
    querySpy.mockResolvedValueOnce(undefined);

    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld' },
    });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(404);
    expect(body.error).toBe('No players found');
  });
});
