import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';
import { handler } from './myActiveTransfers.js';
import createHttpEvent from '@/testing/createHttpEvent.js';

// Mock the getUser function
jest.mock('@/utils/getUser.js', () => ({
  getUser: jest.fn().mockReturnValue('test-user-id'),
}));

// Mock repositories
const mockTransferRepository = {
  getTransferRequestsByBuyer: jest.fn(),
  getTransferRequestsBySeller: jest.fn(),
};

const mockManagerRepository = {
  getManagerById: jest.fn(),
};

describe('myActiveTransfers', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return active transfers for authenticated user', async () => {
    // Mock manager with team
    const mockManager = {
      managerId: 'test-user-id',
      team: {
        teamId: 'test-team-id',
        teamName: 'Test Team',
      },
    };

    // Mock transfer requests
    const buyerRequests = [
      {
        id: 'request-1',
        buyer: { teamId: 'test-team-id' },
        seller: { teamId: 'other-team-id' },
        player: { playerId: 'player-1' },
        value: 100000,
        date: Date.now(),
      },
    ];

    const sellerRequests = [
      {
        id: 'request-2',
        buyer: { teamId: 'other-team-id-2' },
        seller: { teamId: 'test-team-id' },
        player: { playerId: 'player-2' },
        value: 150000,
        date: Date.now(),
      },
    ];

    mockManagerRepository.getManagerById.mockResolvedValue(mockManager);
    mockTransferRepository.getTransferRequestsByBuyer.mockResolvedValue(buyerRequests);
    mockTransferRepository.getTransferRequestsBySeller.mockResolvedValue(sellerRequests);

    const event = createHttpEvent({
      context: {
        repositories: {
          transferRepository: mockTransferRepository,
          managerRepository: mockManagerRepository,
        },
      },
    });

    const result = await handler(event);

    expect(result.statusCode).toBe(200);
    const responseBody = JSON.parse(result.body);
    expect(responseBody).toHaveLength(2);
    expect(responseBody).toEqual([...buyerRequests, ...sellerRequests]);

    expect(mockManagerRepository.getManagerById).toHaveBeenCalledWith('test-user-id');
    expect(mockTransferRepository.getTransferRequestsByBuyer).toHaveBeenCalledWith('test-team-id');
    expect(mockTransferRepository.getTransferRequestsBySeller).toHaveBeenCalledWith('test-team-id');
  });

  it('should return 401 for unauthenticated user', async () => {
    (getUser as jest.Mock).mockReturnValue(null);

    const event = createHttpEvent({
      context: {
        repositories: {
          transferRepository: mockTransferRepository,
          managerRepository: mockManagerRepository,
        },
      },
    });

    const result = await handler(event);

    expect(result.statusCode).toBe(401);
    expect(JSON.parse(result.body)).toEqual({ error: 'Unauthorized' });
  });

  it('should return 404 when manager not found', async () => {
    mockManagerRepository.getManagerById.mockResolvedValue(null);

    const event = createHttpEvent({
      context: {
        repositories: {
          transferRepository: mockTransferRepository,
          managerRepository: mockManagerRepository,
        },
      },
    });

    const result = await handler(event);

    expect(result.statusCode).toBe(404);
    expect(JSON.parse(result.body)).toEqual({ error: 'Manager or team not found' });
  });

  it('should return 404 when manager has no team', async () => {
    const mockManager = {
      managerId: 'test-user-id',
      team: null,
    };

    mockManagerRepository.getManagerById.mockResolvedValue(mockManager);

    const event = createHttpEvent({
      context: {
        repositories: {
          transferRepository: mockTransferRepository,
          managerRepository: mockManagerRepository,
        },
      },
    });

    const result = await handler(event);

    expect(result.statusCode).toBe(404);
    expect(JSON.parse(result.body)).toEqual({ error: 'Manager or team not found' });
  });

  it('should return 500 on repository error', async () => {
    const mockManager = {
      managerId: 'test-user-id',
      team: {
        teamId: 'test-team-id',
        teamName: 'Test Team',
      },
    };

    mockManagerRepository.getManagerById.mockResolvedValue(mockManager);
    mockTransferRepository.getTransferRequestsByBuyer.mockRejectedValue(new Error('Database error'));

    const event = createHttpEvent({
      context: {
        repositories: {
          transferRepository: mockTransferRepository,
          managerRepository: mockManagerRepository,
        },
      },
    });

    const result = await handler(event);

    expect(result.statusCode).toBe(500);
    expect(JSON.parse(result.body)).toEqual({ error: 'Internal server error' });
  });
});
