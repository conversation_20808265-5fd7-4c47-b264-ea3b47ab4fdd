import { sendFixturesToQueue, debugProcessNextFixtures, updatePlayerEnergy } from './fixtureSimulationUtils.js';
import { Fixture } from '@/entities/Fixture.js';
import { FixtureRepository } from '@/storage-interface/fixtures/index.js';
import { SQS } from '@/services/sqs/sqs.js';
import { logger } from '@/utils/logger.js';

import { PlayerFactory } from "@/testing/factories/playerFactory.js";

jest.mock('@/services/sqs/sqs.js');
jest.mock('@/utils/logger.js');

describe('updatePlayerEnergy',()=>{
  const lastMatchPlayed = Date.now() - 1000 * 60 * 60 * 14 // 14 hours ago
  const mockPlayers = PlayerFactory.batch(5, { energy: 50, lastMatchPlayed: lastMatchPlayed });
  it('should update player energy correctly', () => {
    const energiesBefore = mockPlayers.map(player => player.energy);
    updatePlayerEnergy(mockPlayers);
    const energiesAfter = mockPlayers.map(player => player.energy);
    for (let i = 0; i < energiesBefore.length; i++) {
      expect(energiesAfter[i]).toBeGreaterThanOrEqual(energiesBefore[i]);
    }
  })
})

describe('sendFixturesToQueue', () => {
  let sendSpy;
  beforeEach(()=>{
    sendSpy = jest.spyOn(SQS.prototype, 'send');
  })

  const fixtures: Fixture[] = [
    {
      fixtureId: 'fixture1',
      gameworldId: 'gameworld1',
      league: { id: 'league1' },
      homeTeam: { teamId: 'home1', teamName: 'Home Team 1' },
      awayTeam: { teamId: 'away1', teamName: 'Away Team 1' },
      date: 1741165200000,
      played: false,
    },
  ];

  beforeEach(() => {
    jest.resetAllMocks();
    process.env.QUEUE_URL = 'https://sqs.queue.url';
  });

  it('sends valid fixtures to SQS', async () => {
    await sendFixturesToQueue(fixtures);
    expect(sendSpy).toHaveBeenCalledTimes(1);
    expect(sendSpy).toHaveBeenCalledWith(
      process.env.QUEUE_URL,
      expect.stringContaining('"fixtureId":"fixture1"')
    );
  });

  it('throws an error if QUEUE_URL is not set', async () => {
    delete process.env.QUEUE_URL;
    await expect(sendFixturesToQueue(fixtures)).rejects.toThrow('QUEUE_URL environment variable not set');
  });

  it('throws an error for invalid fixture payload', async () => {
    const invalidFixtures = [
      {
        ...fixtures[0],
        homeTeam: { teamId: '', teamName: '' }, // Invalid team data
      },
    ];
    await expect(sendFixturesToQueue(invalidFixtures)).rejects.toThrow('Invalid fixture payload for SQS');
  });
});

describe('debugProcessNextFixtures', () => {
  const mockFixtureRepository: jest.Mocked<FixtureRepository> = {
    getDueFixtures: jest.fn(),
  } as any;

  const fixtures: Fixture[] = [
    {
      fixtureId: 'fixture1',
      gameworldId: 'gameworld1',
      league: { id: 'league1' },
      homeTeam: { teamId: 'home1', teamName: 'Home Team 1' },
      awayTeam: { teamId: 'away1', teamName: 'Away Team 1' },
      date: 1741165200000,
      played: false,
    },
    {
      fixtureId: 'fixture2',
      gameworldId: 'gameworld1',
      league: { id: 'league1' },
      homeTeam: { teamId: 'home2', teamName: 'Home Team 2' },
      awayTeam: { teamId: 'away2', teamName: 'Away Team 2' },
      date: 1741165200000,
      played: false,
    },
  ];

  it('returns unplayed fixtures sorted by date', async () => {
    jest.spyOn(global, 'Date').mockImplementation(() => new Date(1741165200000) as any);
    mockFixtureRepository.getDueFixtures.mockResolvedValue(fixtures);

    const result = await debugProcessNextFixtures(mockFixtureRepository, 'gameworld1', 'league1');
    expect(result).toEqual(fixtures);
    expect(logger.debug).toHaveBeenCalledWith('Sending 2 fixtures to SQS', expect.any(Object));
  });

  it('returns an empty array if no unplayed fixtures are found', async () => {
    mockFixtureRepository.getDueFixtures.mockResolvedValue([]);
    const result = await debugProcessNextFixtures(mockFixtureRepository, 'gameworld1', 'league1');
    expect(result).toEqual([]);
    expect(logger.debug).toHaveBeenCalledWith('No unplayed fixtures found', expect.any(Object));
  });
});