import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';

interface PathParameters {
  gameworldId: string;
  leagueId: string;
  fixtureId: string;
}

export type GetFixtureEvent = HttpEvent<void, PathParameters, void>;

const main = async function (event: GetFixtureEvent) {
  const { fixtureRepository } = event.context.repositories;
  const fixture = await fixtureRepository.getFixture(
    event.pathParameters.gameworldId,
    event.pathParameters.leagueId,
    event.pathParameters.fixtureId
  );

  if (!fixture) {
    return buildResponse(404, JSON.stringify({ error: 'Fixture not found' }));
  }

  return buildResponse(200, JSON.stringify(fixture));
};

export const handler = httpMiddify(main, {});
