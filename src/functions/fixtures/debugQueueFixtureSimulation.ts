import { Fixture } from '@/entities/Fixture.js';
import {
  debugProcessNextFixtures,
  sendFixturesToQueue,
} from '@/functions/fixtures/fixtureSimulationUtils.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { logger } from '@/utils/logger.js';

interface PathParameters {
  gameworldId: string;
  leagueId: string;
}

interface QueryParameters {
  fixtureId?: string;
}

export type DebugQueueFixtureSimulationEvent = HttpEvent<void, PathParameters, QueryParameters>;

const main = async function (event: DebugQueueFixtureSimulationEvent) {
  const { fixtureRepository } = event.context.repositories;

  logger.debug('Debug queue fixture simulation', {
    gameworldId: event.pathParameters.gameworldId,
    leagueId: event.pathParameters.leagueId,
    fixtureId: event.queryStringParameters?.fixtureId,
  });

  let fixturesToSimulate: Fixture[];
  if (event.queryStringParameters?.fixtureId) {
    const fixture = await fixtureRepository.getFixture(
      event.pathParameters.gameworldId,
      event.pathParameters.leagueId,
      event.queryStringParameters.fixtureId
    );
    if (!fixture) {
      return buildResponse(404, JSON.stringify({ error: 'Fixture not found' }));
    }
    fixturesToSimulate = [fixture];
  } else {
    fixturesToSimulate = await debugProcessNextFixtures(
      fixtureRepository,
      event.pathParameters.gameworldId,
      event.pathParameters.leagueId
    );
    logger.debug('Fixtures to simulate', { fixturesToSimulate });
  }

  await sendFixturesToQueue(fixturesToSimulate);

  const response = buildResponse(200, JSON.stringify({ fixturesToSimulate }));
  return Promise.resolve(response);
};
export const handler = httpMiddify(main, {});
