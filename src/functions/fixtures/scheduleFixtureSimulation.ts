import { sendFixturesToQueue } from '@/functions/fixtures/fixtureSimulationUtils.js';
import { eventMiddify } from '@/middleware/event/index.js';
import { EventHandler, EventWithRepositories } from '@/middleware/event/types.js';
import { logger } from '@/utils/logger.js';

/**
 * This lambda is triggered by EventBridge at 10am and 8pm UK time every day
 * It finds all unplayed fixtures with dates in the past and sends them to SQS for simulation
 */
const main: EventHandler<EventWithRepositories, void> = async function (event): Promise<void> {
  const { fixtureRepository } = event.context.repositories;

  logger.debug('Starting scheduled fixture simulation');

  // Find all fixtures that are due but not played yet
  const dueFixtures = await fixtureRepository.getDueFixtures();

  if (dueFixtures.length === 0) {
    logger.info('No fixtures due for simulation');
    return;
  }

  logger.debug('Fixtures due for simulation', { firstFixture: dueFixtures[0] });

  // Send fixtures to SQS for simulation
  await sendFixturesToQueue(dueFixtures);

  logger.info(`Successfully queued ${dueFixtures.length} fixtures for simulation`);
};

export const handler = eventMiddify(main);
