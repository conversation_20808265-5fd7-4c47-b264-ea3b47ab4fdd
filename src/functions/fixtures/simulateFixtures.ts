import { Fixture } from '@/entities/Fixture.js';
import { Player } from '@/entities/Player.js';
import { PlayerMatchHistory } from '@/entities/PlayerMatchHistory.js';
import { PlayerOverallStats } from '@/entities/PlayerOverallStats.js';
import { Team } from '@/entities/Team.js';
import { sqsMiddify } from '@/middleware/sqs/index.js';
import { SQSEvent } from '@/middleware/sqs/types.js';
import { MatchEngine } from '@/simulation/match-engine.js';
import { sortPlayersByPosition } from '@/simulation/player-utils.js';
import { GamePlayer, Team as GameTeam, SimulationResult } from '@/simulation/types.js';
import { PlayerRepository } from '@/storage-interface/players/index.js';
import { TeamRepository } from '@/storage-interface/teams/team-repository.interface.js';
import { calculateMatchExpenses, calculateMatchIncome } from '@/utils/economy.js';
import { logger } from '@/utils/logger.js';
import { seededRandom, setAndReturnSeededRandom } from '@/utils/seeded-random.js';
import { Reference } from '@mikro-orm/core';

interface SimulateFixtureBody {
  fixtureId: string;
  gameworldId: string;
  leagueId: string;
  homeTeamId: string;
  awayTeamId: string;
}

type SimulateFixturesEvent = SimulateFixtureBody;

/**
 * TODO! The db queries in here need wrapping in a transaction!
 */

async function getTeam(teamRepository: TeamRepository, teamId: string, gameworldId: string) {
  const team = await teamRepository.getTeam(gameworldId, teamId, true);
  if (!team) {
    logger.error('Failed to get team', { teamId, gameworldId });
    return null;
  }
  return team;
}

function convertTeam(team: Team): GameTeam {
  let players: GamePlayer[];
  // if there is no manager, then the team is not managed and we should try to sort the players
  // by logical position
  if (!team.manager) {
    players = sortPlayersByPosition(team.players.getItems());
  } else {
    players = team.players.map((player) => {
      const result: GamePlayer = {
        player: player,
        hasBeenSubbed: false,
        isInjured: false,
        stats: {
          yellowCards: 0,
          redCards: 0,
          passesCompleted: 0,
          passesAttempted: 0,
          successfulBallCarries: 0,
          ballCarriesAttempted: 0,
          shots: 0,
          shotsOnTarget: 0,
          goals: 0,
          saves: 0,
          tackles: 0,
          fouls: 0,
        },
      };
      return result;
    });
  }
  return {
    gameworldId: team.gameworldId,
    teamId: team.teamId,
    teamName: team.teamName,
    standings: {
      points: team.points,
      goalsFor: team.goalsFor,
      goalsAgainst: team.goalsAgainst,
      wins: team.wins,
      draws: team.draws,
      losses: team.losses,
      played: team.played,
    },
    players,
  };
}

function updateTeamStandings(
  teamRepository: TeamRepository,
  result: SimulationResult,
  homeGameTeam: GameTeam,
  awayGameTeam: GameTeam,
  homeTeam: Team,
  awayTeam: Team
) {
  const score = result.stats.score;

  const homeIncome = calculateMatchIncome(homeTeam, awayTeam);
  const homeExpenditure = calculateMatchExpenses(homeTeam, true);
  const awayExpenditure = calculateMatchExpenses(awayTeam, false);

  logger.debug(
    `Home income: ${homeIncome}, home expenditure: ${homeExpenditure}, away expenditure: ${awayExpenditure}`
  );

  // Update home team
  return Promise.all([
    teamRepository.updateTeamStandings(
      homeGameTeam.teamId,
      homeGameTeam.gameworldId,
      {
        played: (homeGameTeam.standings.played ?? 0) + 1,
        points:
          homeGameTeam.standings.points + (score[0] > score[1] ? 3 : score[0] === score[1] ? 1 : 0),
        goalsFor: homeGameTeam.standings.goalsFor + score[0],
        goalsAgainst: homeGameTeam.standings.goalsAgainst + score[1],
        wins: homeGameTeam.standings.wins + (score[0] > score[1] ? 1 : 0),
        draws: homeGameTeam.standings.draws + (score[0] === score[1] ? 1 : 0),
        losses: homeGameTeam.standings.losses + (score[0] < score[1] ? 1 : 0),
      },
      false
    ),
    teamRepository.updateTeamStandings(
      awayGameTeam.teamId,
      awayGameTeam.gameworldId,
      {
        played: (awayGameTeam.standings.played ?? 0) + 1,
        points:
          awayGameTeam.standings.points + (score[1] > score[0] ? 3 : score[0] === score[1] ? 1 : 0),
        goalsFor: awayGameTeam.standings.goalsFor + score[1],
        goalsAgainst: awayGameTeam.standings.goalsAgainst + score[0],
        wins: awayGameTeam.standings.wins + (score[1] > score[0] ? 1 : 0),
        draws: awayGameTeam.standings.draws + (score[0] === score[1] ? 1 : 0),
        losses: awayGameTeam.standings.losses + (score[1] < score[0] ? 1 : 0),
      },
      false
    ),
    teamRepository.updateTeamBalance(
      homeGameTeam.teamId,
      homeGameTeam.gameworldId,
      homeIncome - homeExpenditure
    ),
    teamRepository.updateTeamBalance(
      awayGameTeam.teamId,
      awayGameTeam.gameworldId,
      -awayExpenditure
    ),
  ]);
}

async function updatePlayerStats(
  playerRepository: PlayerRepository,
  fixtureId: string,
  players: GamePlayer[]
) {
  logger.debug('Player list', { players: players.map((p) => p.player.playerId) });
  const updates = players.map((gamePlayer) => {
    const player = gamePlayer.player;
    const matchStats = gamePlayer.stats;

    // Check if player already has a match history entry for this fixture
    const existingEntry = player.matchHistory?.isInitialized()
      ? player.matchHistory.getItems().find((history) => history.fixtureId === fixtureId)
      : null;

    if (!existingEntry) {
      // Create new match history entry
      logger.debug('Updating player stats', { player, matchStats, fixtureId });
      const newMatchHistory = new PlayerMatchHistory();
      newMatchHistory.fixtureId = fixtureId;
      newMatchHistory.fixture = Reference.createFromPK(Fixture, fixtureId);
      newMatchHistory.player = Reference.createFromPK(Player, player.playerId);
      newMatchHistory.yellowCards = matchStats.yellowCards;
      newMatchHistory.redCards = matchStats.redCards;
      newMatchHistory.passesCompleted = matchStats.passesCompleted;
      newMatchHistory.passesAttempted = matchStats.passesAttempted;
      newMatchHistory.successfulBallCarries = matchStats.successfulBallCarries;
      newMatchHistory.ballCarriesAttempted = matchStats.ballCarriesAttempted;
      newMatchHistory.shots = matchStats.shots;
      newMatchHistory.shotsOnTarget = matchStats.shotsOnTarget;
      newMatchHistory.goals = matchStats.goals;
      newMatchHistory.saves = matchStats.saves;
      newMatchHistory.tackles = matchStats.tackles;
      newMatchHistory.fouls = matchStats.fouls;

      logger.debug('Adding match history', { newMatchHistory });
      player.matchHistory.add(newMatchHistory);
    } else {
      logger.error('Player already has match history for this fixture', { player, fixtureId });
    }
    // Update existing overall stats or create new ones if they don't exist
    logger.debug('Updating overall stats', { player, matchStats, fixtureId });

    // Check if player already has overall stats
    if (!player.overallStats) {
      // Create new overall stats if none exist
      player.overallStats = new PlayerOverallStats();
      player.overallStats.player = Reference.createFromPK(Player, player.playerId);
    }

    // Update the stats by adding match stats to existing values
    player.overallStats.yellowCards =
      (player.overallStats.yellowCards ?? 0) + matchStats.yellowCards;
    player.overallStats.redCards = (player.overallStats.redCards ?? 0) + matchStats.redCards;
    player.overallStats.passesCompleted =
      (player.overallStats.passesCompleted ?? 0) + matchStats.passesCompleted;
    player.overallStats.passesAttempted =
      (player.overallStats.passesAttempted ?? 0) + matchStats.passesAttempted;
    player.overallStats.successfulBallCarries =
      (player.overallStats.successfulBallCarries ?? 0) + matchStats.successfulBallCarries;
    player.overallStats.ballCarriesAttempted =
      (player.overallStats.ballCarriesAttempted ?? 0) + matchStats.ballCarriesAttempted;
    player.overallStats.shots = (player.overallStats.shots ?? 0) + matchStats.shots;
    player.overallStats.shotsOnTarget =
      (player.overallStats.shotsOnTarget ?? 0) + matchStats.shotsOnTarget;
    player.overallStats.goals = (player.overallStats.goals ?? 0) + matchStats.goals;
    player.overallStats.saves = (player.overallStats.saves ?? 0) + matchStats.saves;
    player.overallStats.tackles = (player.overallStats.tackles ?? 0) + matchStats.tackles;
    player.overallStats.fouls = (player.overallStats.fouls ?? 0) + matchStats.fouls;

    player.lastMatchPlayed = Date.now();

    // Reset these stats so they dont go on forever
    if (player.suspendedForGames > 0) {
      player.suspendedForGames -= 1;
    }
    if (player.injuredUntil && player.injuredUntil < Date.now()) {
      player.injuredUntil = undefined;
    }

    // Add penalties
    if (matchStats.redCards > 0) {
      player.suspendedForGames = 2;
    }
    if (gamePlayer.isInjured) {
      // randomize number of hours between 1 and 96
      const randomHours = Math.floor(seededRandom() * 96) + 1;
      player.injuredUntil = Date.now() + 1000 * 60 * 60 * randomHours;
    }

    return player;
  });

  // Update the players
  await playerRepository.updatePlayerStats(updates);
}

const main = async function (event: SQSEvent<SimulateFixturesEvent>): Promise<void> {
  logger.debug('Simulating fixtures');

  const { fixtureRepository, teamRepository, playerRepository } = event.context.repositories;

  for (const record of event.Records) {
    const fixture = record.body;
    logger.debug('Simulating fixture', { fixture });

    // Get teams
    const homeTeam = await getTeam(teamRepository, fixture.homeTeamId, fixture.gameworldId);
    const awayTeam = await getTeam(teamRepository, fixture.awayTeamId, fixture.gameworldId);

    if (!homeTeam || !awayTeam) {
      logger.error('Teams not found', { fixture });
      continue;
    }

    const seed = setAndReturnSeededRandom();

    // Run simulation
    const homeTeamConverted = convertTeam(homeTeam);
    const awayTeamConverted = convertTeam(awayTeam);
    const matchEngine = new MatchEngine(homeTeamConverted, awayTeamConverted);
    logger.debug('Running simulation');
    const result = matchEngine.simulate();

    // Store results
    logger.debug('Updating fixture result');
    await fixtureRepository.updateFixtureResult(
      fixture.gameworldId,
      fixture.leagueId,
      fixture.fixtureId,
      seed,
      result.stats,
      result.events
    );
    // Update team standings
    logger.debug('Updating team standings');
    await updateTeamStandings(
      teamRepository,
      result,
      homeTeamConverted,
      awayTeamConverted,
      homeTeam,
      awayTeam
    );

    // Update player stats
    logger.debug('Updating player stats');
    await updatePlayerStats(playerRepository, fixture.fixtureId, [
      ...homeTeamConverted.players,
      ...awayTeamConverted.players,
    ]);
    await teamRepository.flush();
    logger.debug('Fixture simulation complete');
  }

  return Promise.resolve();
};

export const handler = sqsMiddify<SimulateFixturesEvent>(main, {});
