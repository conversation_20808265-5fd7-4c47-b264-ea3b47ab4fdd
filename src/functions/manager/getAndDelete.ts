import { AvailableTeam } from '@/entities/AvailableTeam.js';
import { TeamRepository } from '@/storage-interface/teams/team-repository.interface.js';

export async function getAndDelete(teamRepository: TeamRepository): Promise<AvailableTeam | null> {
  const team = await teamRepository.getRandomAvailableTeam();
  if (!team) {
    return null;
  }

  await teamRepository.deleteAvailableTeam(team);
  return team;
}
