import { Manager } from '@/entities/Manager.js';
import { Team } from '@/entities/Team.js';
import { getAndDelete } from '@/functions/manager/getAndDelete.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { logger } from '@/utils/logger.js';
import { Reference } from '@mikro-orm/core';
import { v4 as uuidv4 } from 'uuid';

type DevManagerEvent = HttpEvent<void, void, void>;

const main = async function (event: DevManagerEvent) {
  const { teamRepository, managerRepository } = event.context.repositories;
  if (process.env.STAGE !== 'dev') {
    logger.error('DevManager should only be called in dev stage');
    return Promise.resolve(
      buildResponse(
        500,
        JSON.stringify({
          message: 'DevManager should only be called in dev stage',
        })
      )
    );
  }

  try {
    // get a team from the available teams table
    const team = await getAndDelete(teamRepository);
    if (!team) {
      throw new Error('No available teams');
    }

    const manager = new Manager();
    manager.managerId = process.env.DEBUG_USER_ID || uuidv4();
    manager.createdAt = new Date().getTime();
    manager.lastActive = new Date().getTime();
    manager.team = Reference.createFromPK(Team, team.teamId);
    manager.gameworldId = team.gameworldId;
    manager.scoutTokens = 3;
    manager.superScoutTokens = 0;

    await managerRepository.createManager(manager);

    return buildResponse(200, JSON.stringify({ message: 'Manager created' }));
  } catch (error) {
    logger.error('Error saving user:', { error });
    throw error;
  }
};

export const handler = httpMiddify(main, {});
