import { Fixture } from '@/entities/Fixture.js';
import { League } from '@/entities/League.js';
import { Team } from '@/entities/Team.js';
import { generateFixtures } from './fixtures.js';

describe('generateFixtures', () => {
  function generateMockTeam(id: number): Team {
    const team = new Team();
    team.teamId = `team${id}`;
    team.gameworldId = 'gameworld1';
    team.tier = 1;
    team.teamName = `teamName${id}`;

    // Create a simple league object
    const league = new League();
    league.id = 'league1';
    league.gameworldId = 'gameworld1';
    league.name = 'Test League';
    league.tier = 1;

    // Set the league property directly
    // @ts-ignore - we're mocking for tests
    team.league = { id: 'league1', isInitialized: () => true };

    return team;
  }

  it('should generate the correct number of fixtures for 15 teams', () => {
    const teams: Team[] = Array.from({ length: 15 }, (_, i) => generateMockTeam(i + 1));

    const fixtures = generateFixtures(teams);

    expect(fixtures).toHaveLength(105);
  });

  it.each(Array.from({ length: 15 }, (_, i) => i + 1))(
    'team%i should have exactly 14 games',
    (teamNumber) => {
      const teams: Team[] = Array.from({ length: 15 }, (_, i) => generateMockTeam(i + 1));
      const fixtures = generateFixtures(teams);

      const teamFixtures = fixtures.filter(
        (fixture: Fixture) =>
          fixture.homeTeam.teamId === `team${teamNumber}` ||
          fixture.awayTeam.teamId === `team${teamNumber}`
      );

      expect(teamFixtures).toHaveLength(14);
    }
  );

  it('should alternate home and away matches', () => {
    const teams: Team[] = Array.from({ length: 5 }, (_, i) => generateMockTeam(i + 1));

    const fixtures = generateFixtures(teams);

    const homeMatches = fixtures.filter((fixture: Fixture) => fixture.homeTeam.teamId === 'team1');
    const awayMatches = fixtures.filter((fixture: Fixture) => fixture.awayTeam.teamId === 'team1');

    expect(homeMatches).toHaveLength(2);
    expect(awayMatches).toHaveLength(2);
  });

  it('should throw an error if teams array is empty', () => {
    expect(() => generateFixtures([])).toThrow('Need at least 2 teams to generate fixtures');
  });
});
