import { handler } from '@/functions/generate/player.js';
import DynamoDbService from '@/services/database/dynamo/dynamo-db-service.js';
import createSqsEvent from '@/testing/createSqsEvent.js';
import { TeamCreatedEvent } from '@/types/generated/index.js';

jest.mock('@/services/database/dynamo/dynamo-db-service');

describe('Generate Player', () => {
  let defaultEvent: any;
  const context = {} as any; // Mock context object
  const insertSpy = jest.spyOn(DynamoDbService.prototype, 'batchInsert');

  beforeEach(() => {
    process.env.PLAYERS_TABLE_NAME = 'playersTable';

    const testBody: TeamCreatedEvent = {
      leagueId: '',
      requiredPlayers: 15,
      teamId: 'testTeamId',
      gameworldId: 'testGameworldId',
      tier: 1,
    };
    defaultEvent = createSqsEvent([{ body: testBody }]);

    insertSpy.mockResolvedValue({ successful: [], failed: [] });

    // Use fake timers and set the system time
    jest.useFakeTimers();
    jest.setSystemTime(new Date(1740178572294));
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.useRealTimers(); // Restore real timers
  });

  it('should generate the correct number of players', async () => {
    await handler(defaultEvent, context);

    // Verify that batchInsert was called with the correct number of items
    expect(insertSpy).toHaveBeenCalledTimes(1);
    expect(insertSpy).toHaveBeenCalledWith('playersTable', expect.any(Array));
    const callArgs = insertSpy.mock.calls[0]![1]; // Gets the second argument of the first call
    expect(callArgs).toHaveLength(15);
  });

  it('should be used to debug player attributes', async () => {
    await handler(defaultEvent, context);

    // Add an assertion to avoid ESLint error
    expect(Date.now()).toBe(1740178572294);
  });
});
