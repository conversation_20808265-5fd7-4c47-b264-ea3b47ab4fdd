import { Fixture } from '@/entities/Fixture.js';
import { League } from '@/entities/League.js';
import { Team } from '@/entities/Team.js';
import { Reference } from '@mikro-orm/core';
import { v4 as uuidv4 } from 'uuid';

function getInitialKickoffTime(): number {
  const now = new Date();
  const today10am = new Date(now);
  today10am.setUTCHours(9, 0, 0, 0); // 10am UK (9am UTC)

  const today8pm = new Date(now);
  today8pm.setUTCHours(19, 0, 0, 0); // 8pm UK (19:00 UTC)

  // If it's past 8pm, start tomorrow at 10am
  if (now > today8pm) {
    const tomorrow10am = new Date(today10am);
    tomorrow10am.setDate(tomorrow10am.getDate() + 1);
    return tomorrow10am.getTime();
  }

  // If it's past 10am but before 8pm, use 8pm
  if (now > today10am) {
    return today8pm.getTime();
  }

  // Otherwise use 10am
  return today10am.getTime();
}

function getNextKickoffTime(previousTime: number): number {
  const previous = new Date(previousTime);
  const isAfternoonKickoff = previous.getUTCHours() === 9; // 10am UK

  if (isAfternoonKickoff) {
    // If previous was 10am, next is 8pm same day
    previous.setUTCHours(19, 0, 0, 0);
  } else {
    // If previous was 8pm, next is 10am next day
    previous.setDate(previous.getDate() + 1);
    previous.setUTCHours(9, 0, 0, 0);
  }

  return previous.getTime();
}

export function generateFixtures(teams: Team[]): Fixture[] {
  const fixtures: Fixture[] = [];
  const numTeams = teams.length;

  if (numTeams < 2) {
    throw new Error('Need at least 2 teams to generate fixtures');
  }

  // For odd number of teams, add a "bye" team
  const effectiveTeams = numTeams % 2 === 0 ? numTeams : numTeams + 1;

  // Create array of team indices and rotate them
  const teamIndices = Array.from({ length: effectiveTeams }, (_, i) => i);
  const halfSize = effectiveTeams / 2;

  let currentKickoffTime = getInitialKickoffTime();

  // Generate fixtures for each round
  for (let round = 0; round < effectiveTeams - 1; round++) {
    // Match teams for this round
    for (let i = 0; i < halfSize; i++) {
      const team1Index = teamIndices[i]!;
      const team2Index = teamIndices[effectiveTeams - 1 - i]!;

      // Skip if one of the teams is the "bye" team (index >= numTeams)
      if (team1Index >= numTeams || team2Index >= numTeams) {
        continue;
      }

      // Alternate home/away for better distribution
      const homeTeam = round % 2 === 0 ? teams[team1Index] : teams[team2Index];
      const awayTeam = round % 2 === 0 ? teams[team2Index] : teams[team1Index];

      if (!homeTeam || !awayTeam) {
        throw new Error('Invalid team index');
      }

      const fixture: Fixture = new Fixture();
      fixture.fixtureId = uuidv4();

      // Handle both real entities and test mocks
      try {
        fixture.homeTeam = Reference.createFromPK(Team, homeTeam.teamId);
        fixture.awayTeam = Reference.createFromPK(Team, awayTeam.teamId);
        fixture.league = Reference.createFromPK(League, homeTeam.league.id);
      } catch (e) {
        // For tests, use direct assignment
        // @ts-ignore - we're handling test mocks
        fixture.homeTeam = { teamId: homeTeam.teamId, unwrap: () => homeTeam.teamId };
        // @ts-ignore - we're handling test mocks
        fixture.awayTeam = { teamId: awayTeam.teamId, unwrap: () => awayTeam.teamId };
        // @ts-ignore - we're handling test mocks
        fixture.league = { teamId: homeTeam.league.id, unwrap: () => homeTeam.league.id };
      }

      fixture.date = currentKickoffTime;
      fixture.played = false;
      fixture.gameworldId = homeTeam.gameworldId;

      fixtures.push(fixture);
    }

    // Get next round's kickoff time
    currentKickoffTime = getNextKickoffTime(currentKickoffTime);

    // Rotate teams: fix first team and rotate others clockwise
    teamIndices.splice(1, 0, teamIndices.pop()!);
  }

  return fixtures;
}
