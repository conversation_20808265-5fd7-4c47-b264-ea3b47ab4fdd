import { AvailableTeam } from '@/entities/AvailableTeam.js';
import { League } from '@/entities/League.js';
import { Team } from '@/entities/Team.js';
import { generateRandomTeamName } from '@/functions/generate/random-team-name.js';
import { Lambda } from '@/middleware/lambda/index.js';
import { sqsMiddify } from '@/middleware/sqs/index.js';
import { SQSEvent } from '@/middleware/sqs/types.js';

import { SQS } from '@/services/sqs/sqs.js';
import { TeamRepository } from '@/storage-interface/teams/team-repository.interface.js';
import { LeagueTableCreatedEvent } from '@/types/generated/league-table-created-event.js';
import { logger } from '@/utils/logger.js';
import { tracer } from '@/utils/tracer.js';
import { Reference } from '@mikro-orm/core';
import { v4 as uuidv4 } from 'uuid';

function generateTeam(gameworldId: string, leagueId: string, tier: number): Team {
  const team = new Team();

  // Set required properties
  team.teamId = uuidv4();
  team.gameworldId = gameworldId;
  team.tier = tier;
  team.teamName = generateRandomTeamName();

  // Set default values
  team.balance = 300000;
  team.played = 0;
  team.points = 0;
  team.goalsFor = 0;
  team.goalsAgainst = 0;
  team.wins = 0;
  team.draws = 0;
  team.losses = 0;
  team.selectionOrder = [];

  team.league = Reference.createFromPK(League, leagueId);

  return team;
}

async function generateTeams(
  body: LeagueTableCreatedEvent,
  teamRepository: TeamRepository
): Promise<{ teams: Team[] }> {
  const startTime = Date.now();
  logger.debug('Starting team generation', {
    requiredTeams: body.requiredTeams,
    leagueId: body.leagueId,
    gameworldId: body.gameworldId,
    timestamp: startTime,
  });

  // Generate team objects
  const teamGenStart = Date.now();
  const teams: Team[] = [];
  for (let i = 0; i < body.requiredTeams; i++) {
    teams.push(generateTeam(body.gameworldId, body.leagueId, body.tier));
  }
  logger.debug('Team objects generated', {
    count: teams.length,
    duration: Date.now() - teamGenStart,
  });

  if (teams.length === 0) {
    return { teams: [] };
  }

  if (!process.env.TEAMS_TABLE_NAME) {
    throw new Error('Invalid team table name');
  }
  try {
    // Insert teams
    const teamInsertStart = Date.now();
    logger.debug('Starting team insertion', { count: teams.length });
    await teamRepository.batchInsertTeams(teams);
    logger.debug('Teams insertion completed', {
      duration: Date.now() - teamInsertStart,
    });

    // Add teams to AVAILABLE_TEAMS_TABLE_NAME if availableToManage is true
    if (body.availableToManage) {
      const availableTeamsStart = Date.now();
      logger.debug('Starting available teams insertion');
      const availableTeams: AvailableTeam[] = teams.map((team) => {
        const availableTeam = new AvailableTeam();
        availableTeam.id = uuidv4();
        availableTeam.gameworldId = team.gameworldId;
        availableTeam.teamId = team.teamId; // Direct ID assignment instead of reference
        return availableTeam;
      });
      await teamRepository.batchInsertAvailableTeams(availableTeams);
      logger.debug('Available teams insertion completed', {
        duration: Date.now() - availableTeamsStart,
      });
    }

    // Trigger fixtures generation via Lambda invoke
    const fixturesStart = Date.now();
    logger.debug('Triggering fixtures generation via Lambda invoke');

    // Create Lambda client
    const lambdaClient = new Lambda({ tracer });

    // Invoke fixtures lambda directly
    const generateFixturesLambdaArn = process.env.GENERATE_FIXTURES_LAMBDA_ARN!;

    await lambdaClient.eventInvoke(
      JSON.stringify({
        gameworldId: body.gameworldId,
        leagueId: body.leagueId,
      }),
      generateFixturesLambdaArn
    );

    logger.debug('Fixtures generation triggered', {
      duration: Date.now() - fixturesStart,
    });

    logger.debug('Team generation process completed', {
      totalDuration: Date.now() - startTime,
    });

    return { teams };
  } catch (error) {
    logger.error('Team generation process failed', {
      error,
      duration: Date.now() - startTime,
    });
    return { teams: [] };
  }
}

/**
 * Generate a team in response to an SQS event
 * @param event
 */
async function main(event: SQSEvent<LeagueTableCreatedEvent>) {
  const { teamRepository } = event.context.repositories;

  const mainStart = Date.now();
  logger.debug('Starting main handler', {
    recordCount: event.Records.length,
  });

  const results = await Promise.all(
    event.Records.map((record) => generateTeams(record.body, teamRepository))
  );
  const aggregatedResults = {
    teams: results.flatMap((r) => r.teams),
  };

  logger.debug('Teams created successfully', {
    duration: Date.now() - mainStart,
  });

  // SQS Publishing
  const sqsStart = Date.now();
  const sqsClient = new SQS({ tracer });
  // Process messages in batches for better logging
  const batchSize = 10;

  logger.debug('Starting SQS message publishing', {
    totalMessages: aggregatedResults.teams.length,
  });

  for (let i = 0; i < aggregatedResults.teams.length; i += batchSize) {
    const batchStart = Date.now();
    const batch = aggregatedResults.teams.slice(i, i + batchSize);
    const promises = batch.map((team) =>
      sqsClient.send(
        process.env.PLAYER_QUEUE_URL!,
        JSON.stringify({
          gameworldId: team.gameworldId,
          leagueId: team.league.id,
          tier: team.tier,
          teamId: team.teamId,
          requiredPlayers: 15,
        })
      )
    );

    await Promise.all(promises);
    logger.debug('SQS batch sent', {
      batchNumber: Math.floor(i / batchSize) + 1,
      batchSize: batch.length,
      duration: Date.now() - batchStart,
    });
  }

  logger.debug('Handler execution completed', {
    totalDuration: Date.now() - mainStart,
    sqsPublishingDuration: Date.now() - sqsStart,
  });

  return;
}

export const handler = sqsMiddify<LeagueTableCreatedEvent>(main, {});
