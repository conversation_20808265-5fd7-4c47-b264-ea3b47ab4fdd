import { handler } from '@/functions/generate/unattached-players.js';
import { DBPlayer } from '@/model/player.js';
import DynamoDbService from '@/services/database/dynamo/dynamo-db-service.js';
import createSqsEvent from '@/testing/createSqsEvent.js';
import { GenerateUnattachedPlayersEvent } from '@/types/generated/index.js';
import { logger } from '@/utils/logger.js';

jest.mock('@/services/database/dynamo/dynamo-db-service');

describe('Generate Unattached Players', () => {
  let defaultEvent: any;
  const context = {} as any;
  const insertSpy = jest.spyOn(DynamoDbService.prototype, 'batchInsert');

  beforeEach(() => {
    process.env.TRANSFER_LISTED_PLAYERS_TABLE_NAME = 'transferListedPlayersTable';

    const testBody: GenerateUnattachedPlayersEvent = {
      gameworldId: 'testGameworldId',
      requiredPlayers: 75,
    };
    defaultEvent = createSqsEvent([{ body: testBody }]);

    insertSpy.mockResolvedValue({ successful: [], failed: [] });

    // Use fake timers and set the system time
    jest.useFakeTimers();
    jest.setSystemTime(new Date(1740178572294));
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.useRealTimers();
  });

  it('should generate the correct number of players (75 per league)', async () => {
    await handler(defaultEvent, context);

    expect(insertSpy).toHaveBeenCalledTimes(1);
    expect(insertSpy).toHaveBeenCalledWith('transferListedPlayersTable', expect.any(Array));
    const callArgs = insertSpy.mock.calls[0]![1] as DBPlayer[];
    expect(callArgs).toHaveLength(75);
  });

  it('should handle multiple league tables in the event', async () => {
    const multipleEvent = createSqsEvent([
      {
        body: {
          gameworldId: 'world1',
          requiredPlayers: 75,
        },
      },
      {
        body: {
          gameworldId: 'world2',
          requiredPlayers: 75,
        },
      },
    ]) as any;

    await handler(multipleEvent, context);

    expect(insertSpy).toHaveBeenCalledTimes(1);
    const players = insertSpy.mock.calls[0]![1] as DBPlayer[];
    expect(players).toHaveLength(150); // 75 players * 2 leagues

    const world1Players = players.filter((p) => p.gameworldId === 'world1');
    const world2Players = players.filter((p) => p.gameworldId === 'world2');

    expect(world1Players).toHaveLength(75);
    expect(world2Players).toHaveLength(75);
  });

  it('should handle database insertion failures', async () => {
    const loggerErrorSpy = jest.spyOn(logger, 'error').mockImplementation();
    insertSpy.mockResolvedValue({
      successful: [],
      failed: [{ error: new Error('Database error'), item: {} }],
    });

    await handler(defaultEvent, context);

    expect(loggerErrorSpy).toHaveBeenCalled();
    loggerErrorSpy.mockRestore();
  });
});
