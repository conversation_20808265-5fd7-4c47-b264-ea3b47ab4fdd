import DynamoDbService from '@/services/database/dynamo/dynamo-db-service.js';
import { SQS } from '@/services/sqs/sqs.js';
import createHttpEvent from '@/testing/createHttpEvent.js';
import { handler } from './gameworld.js';

jest.mock('@/services/database/dynamo/dynamo-db-service');
jest.mock('@/services/sqs/sqs.js');

describe('Generate Gameworld', () => {
  const context = {} as any;
  const insertSpy = jest.spyOn(DynamoDbService.prototype, 'batchInsert');
  const sendSpy = jest.fn().mockResolvedValue(undefined);

  beforeEach(() => {
    process.env.LEAGUES_TABLE_NAME = 'leaguesTable';
    process.env.UNATTACHED_PLAYERS_QUEUE_URL = 'test-unattached-queue-url';
    process.env.TEAM_QUEUE_URL = 'test-team-queue-url';

    insertSpy.mockResolvedValue({ successful: [], failed: [] });

    // Mock SQS constructor and send method
    (SQS as jest.MockedClass<typeof SQS>).mockImplementation(
      () =>
        ({
          sqs: {},
          send: sendSpy,
        }) as unknown as SQS
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it.each([
    { tiers: 1, childLeagues: 3, expected: 1 }, // 1
    { tiers: 2, childLeagues: 3, expected: 4 }, // 1 + 3
    { tiers: 3, childLeagues: 3, expected: 13 }, // 1 + 3 + 9
    { tiers: 4, childLeagues: 3, expected: 40 }, // 1 + 3 + 9 + 27
    { tiers: 3, childLeagues: 2, expected: 7 }, // 1 + 2 + 4
    { tiers: 4, childLeagues: 2, expected: 15 }, // 1 + 2 + 4 + 8
  ])(
    'should create $expected leagues for $tiers tiers and $childLeagues child leagues',
    async ({ tiers, childLeagues, expected }) => {
      const event = createHttpEvent({
        body: {
          tiers,
          childLeagues,
          teamsPerLeague: 15,
        },
        httpMethod: 'POST',
      });

      await handler(event, context);

      expect(insertSpy).toHaveBeenCalledTimes(1);
      expect(insertSpy).toHaveBeenCalledWith(
        'leaguesTable',
        expect.arrayContaining([]) && expect.any(Array)
      );
      const callArgs = insertSpy.mock.calls[0]![1];
      expect(callArgs).toHaveLength(expected);
    }
  );

  it('should set correct promotion/relegation rules based on tier', async () => {
    const event = createHttpEvent({
      body: {
        tiers: 3,
        childLeagues: 2,
        teamsPerLeague: 15,
      },
      httpMethod: 'POST',
    });

    await handler(event, context);

    const callArgs = insertSpy.mock.calls[0]![1];

    // Top tier (tier 1) should have no promotion
    const topTier = callArgs.find((league) => league.tier === 1);
    expect(topTier?.rules.promotionSpots).toBe(0);
    expect(topTier?.rules.relegationSpots).toBe(2);

    // Middle tier (tier 2) should have both promotion and relegation
    const middleTier = callArgs.find((league) => league.tier === 2);
    expect(middleTier?.rules.promotionSpots).toBe(1);
    expect(middleTier?.rules.relegationSpots).toBe(2);

    // Bottom tier (tier 3) should have no relegation
    const bottomTier = callArgs.find((league) => league.tier === 3);
    expect(bottomTier?.rules.promotionSpots).toBe(1);
    expect(bottomTier?.rules.relegationSpots).toBe(0);
  });

  it('should send events to SQS for unattached players and league creation', async () => {
    const event = createHttpEvent({
      body: {
        tiers: 2,
        childLeagues: 2,
        teamsPerLeague: 15,
      },
      httpMethod: 'POST',
    });

    await handler(event, context);

    // Should create 3 leagues (1 parent + 2 children)
    expect(sendSpy).toHaveBeenCalledTimes(4); // 3 league events + 1 unattached players event

    // Verify unattached players event
    expect(sendSpy).toHaveBeenCalledWith(
      'test-unattached-queue-url',
      expect.stringContaining('requiredPlayers')
    );

    // Verify league creation events
    expect(sendSpy).toHaveBeenCalledWith(
      'test-team-queue-url',
      expect.stringContaining('requiredTeams')
    );
  });

  it('should set availableToManage correctly based on tier', async () => {
    const event = createHttpEvent({
      body: {
        tiers: 4,
        childLeagues: 1,
        teamsPerLeague: 15,
      },
      httpMethod: 'POST',
    });

    await handler(event, context);

    // Verify that SQS messages were sent
    expect(sendSpy).toHaveBeenCalled();

    // We can't easily check the content of the messages since they're stringified
    // but we can verify the number of calls
    const sendCalls = sendSpy.mock.calls;
    expect(sendCalls.length).toBeGreaterThan(1);
  });
});
