import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { sendTemplatedEmail } from '@/services/email/index.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { logger } from '@/utils/logger.js';

export type PingEvent = HttpEvent<void, void, void>;

const main = async function (_: PingEvent) {
  logger.debug('Pinging');
  await sendTemplatedEmail(
    ['<EMAIL>'],
    'test email',
    'Hey this is a test email',
    'PING!'
  );
  return Promise.resolve(buildResponse(200, JSON.stringify({ message: 'pong' })));
};

export const handler = httpMiddify(main, { injectRepositories: false });
