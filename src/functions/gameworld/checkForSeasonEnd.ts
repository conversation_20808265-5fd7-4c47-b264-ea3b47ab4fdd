import { eventMiddify } from '@/middleware/event/index.js';
import { Event<PERSON><PERSON><PERSON>, EventWithRepositories } from '@/middleware/event/types.js';
import { SQS } from '@/services/sqs/sqs.js';
import { SeasonEndEvent } from '@/types/generated/season-end-event.js';
import { logger } from '@/utils/logger.js';
import { tracer } from '@/utils/tracer.js';

const sqs = new SQS({ tracer });

/**
 * This lambda is triggered by EventBridge at midnight UK time every day
 * It finds all unplayed fixtures with dates in the past and sends them to SQS for simulation
 */
const main: EventHandler<EventWithRepositories, void> = async function (event): Promise<void> {
  const { gameworldRepository } = event.context.repositories;

  logger.debug('Checking gameworld end dates');

  // Find all fixtures that are due but not played yet
  const completedGameworlds = await gameworldRepository.getCompletedSeasons();

  if (completedGameworlds.length === 0) {
    logger.info('No gameworlds complete');
    return;
  }

  if (!process.env.QUEUE_URL) {
    throw new Error('QUEUE_URL environment variable not set');
  }

  await Promise.all(
    completedGameworlds.map(async (gameworld) => {
      const sqsPayload: SeasonEndEvent = {
        gameworldId: gameworld.id,
      };

      return sqs.send(process.env.QUEUE_URL!, JSON.stringify(sqsPayload));
    })
  );

  logger.info(`Successfully queued ${completedGameworlds.length} gameworlds for processing`);
};

export const handler = eventMiddify(main);
