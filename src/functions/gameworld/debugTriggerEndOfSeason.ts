import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { SQS } from '@/services/sqs/sqs.js';
import { SeasonEndEvent } from '@/types/generated/season-end-event.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { logger } from '@/utils/logger.js';
import { tracer } from '@/utils/tracer.js';

interface PathParameters {
  gameworldId: string;
}

export type DebugTriggerEndOfSeasonEvent = HttpEvent<void, PathParameters, void>;

const sqs = new SQS({ tracer });

const main = async function (event: DebugTriggerEndOfSeasonEvent) {
  const gameworldId = event.pathParameters.gameworldId;
  logger.debug('Triggering end of season processing', { gameworldId });

  const sqsPayload: SeasonEndEvent = {
    gameworldId,
  };

  await sqs.send(process.env.QUEUE_URL!, JSON.stringify(sqsPayload));

  const response = buildResponse(
    200,
    JSON.stringify({
      message: 'End of season processing triggered',
      gameworldId,
    })
  );

  return Promise.resolve(response);
};

export const handler = httpMiddify(main, {});
