/* eslint-disable jest/unbound-method */
// No need to mock database initializer as we're using middleware

// Now import everything else
import { League } from '@/entities/League.js';
import { LeagueRules } from '@/entities/LeagueRules.ts';
import { Team } from '@/entities/Team.js';
import { LeagueProcessor } from '@/functions/league/logic/LeagueProcessorV2.js';
import { handler } from '@/functions/league/processEndOfSeason.js';
import { LeagueFactory } from '@/testing/factories/leagueFactory.js';

// Mock the logger module
jest.mock('@/utils/logger.js', () => ({
  logger: {
    debug: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    info: jest.fn(),
    trace: jest.fn(),
    addContext: jest.fn(),
    logEventIfEnabled: jest.fn(),
  },
}));

// Mock LeagueProcessor
jest.mock('@/functions/league/logic/LeagueProcessorV2.js', () => ({
  LeagueProcessor: {
    sortTeamsInLeague: jest.fn(),
    processPromotionsAndRelegations: jest.fn(),
    getTeamStandings: jest.fn(),
  },
}));

const { logger } = jest.requireMock('@/utils/logger.js');

describe('Process End of Season', () => {
  const context: any = {};
  let mockGetLeaguesByGameworld: jest.Mock;
  let mockUpdateTeamLeagues: jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();

    // Create mock repositories
    const mockLeagueRepo = { getLeaguesByGameworld: jest.fn() };
    const mockTeamRepo = {
      updateTeamLeagues: jest.fn(),
      updateTeamBalance: jest.fn(),
      flush: jest.fn(),
    };

    // Store references to the mock functions
    mockGetLeaguesByGameworld = mockLeagueRepo.getLeaguesByGameworld;
    mockUpdateTeamLeagues = mockTeamRepo.updateTeamLeagues;

    // Add repositories to the context
    context.repositories = {
      leagueRepository: mockLeagueRepo,
      teamRepository: mockTeamRepo,
    };
  });

  it('should process league movements correctly', async () => {
    const team1 = {
      teamId: 'team1',
      leagueId: 'league1',
      teamName: 'Team 1',
      points: 10,
      goalsFor: 20,
      goalsAgainst: 10,
      wins: 3,
      draws: 1,
      losses: 1,
      played: 5,
    } as Team;

    const team2 = {
      teamId: 'team2',
      leagueId: 'league2',
      teamName: 'Team 2',
      points: 12,
      goalsFor: 15,
      goalsAgainst: 5,
      wins: 4,
      draws: 0,
      losses: 1,
      played: 5,
    } as Team;

    const leagues: League[] = [
      LeagueFactory.build({
        tier: 1,
        leagueRules: {
          teamCount: 15,
          promotionSpots: 0,
          relegationSpots: 1,
          minimumPrize: 1000,
          maximumPrize: 5000,
        } as LeagueRules,
        teams: {
          getItems: () => [team1],
          length: 1,
          [0]: team1,
        } as any,
      }),
      {
        id: 'league2',
        gameworldId: 'test-gameworld',
        tier: 2,
        name: 'League 2',
        leagueRules: {
          teamCount: 15,
          promotionSpots: 1,
          relegationSpots: 0,
          minimumPrize: 500,
          maximumPrize: 2500,
        },
        teams: {
          getItems: () => [team2],
          length: 1,
          [0]: team2,
        },
      } as League,
    ];

    const mockMovements = [
      { teamId: 'team1', fromLeagueId: 'league1', toLeagueId: 'league2' },
      { teamId: 'team2', fromLeagueId: 'league2', toLeagueId: 'league1' },
    ];

    // Mock repository methods
    mockGetLeaguesByGameworld.mockResolvedValue(leagues);
    mockUpdateTeamLeagues.mockResolvedValue(void 0);

    // Mock LeagueProcessor methods
    jest.spyOn(LeagueProcessor, 'sortTeamsInLeague').mockImplementation((t) => t);
    jest.spyOn(LeagueProcessor, 'processPromotionsAndRelegations').mockReturnValue(mockMovements);
    jest.spyOn(LeagueProcessor, 'getTeamStandings').mockReturnValue({
      teamName: 'Team 1',
      points: 10,
      goalDiff: 10,
      goalsFor: 20,
      wins: 3,
    });

    const event = {
      Records: [{ body: JSON.stringify({ gameworldId: 'test-gameworld' }) }],
      context: context,
    };
    await handler(event, context);

    // Verify repository calls
    expect(mockGetLeaguesByGameworld).toHaveBeenCalledWith('test-gameworld', true);
    expect(mockUpdateTeamLeagues).toHaveBeenCalledWith(
      expect.arrayContaining([team1, team2]),
      mockMovements,
      'test-gameworld'
    );

    // Verify LeagueProcessor calls
    expect(LeagueProcessor.sortTeamsInLeague).toHaveBeenCalled();
    expect(LeagueProcessor.processPromotionsAndRelegations).toHaveBeenCalledWith(
      expect.any(Map),
      leagues
    );
  });

  it('should handle empty leagues gracefully', async () => {
    // Mock repository methods with empty arrays
    mockGetLeaguesByGameworld.mockResolvedValue([]);

    const event = {
      Records: [{ body: JSON.stringify({ gameworldId: 'test-gameworld' }) }],
      context: context,
    };
    await handler(event, context);

    expect(mockUpdateTeamLeagues).not.toHaveBeenCalled();
    expect(logger.warn).toHaveBeenCalledWith('No leagues found for gameworld', {
      gameworldId: 'test-gameworld',
    });
  });

  it('should handle leagues with no teams gracefully', async () => {
    // Mock repository methods with leagues but no teams
    const emptyLeagues: League[] = [
      {
        id: 'league1',
        gameworldId: 'test-gameworld',
        tier: 1,
        name: 'League 1',
        leagueRules: {
          teamCount: 15,
          promotionSpots: 0,
          relegationSpots: 1,
          leagueId: 'league1',
          minimumPrize: 1000,
          maximumPrize: 5000,
        },
        teams: {
          getItems: () => [],
          length: 0,
        },
      } as League,
    ];

    mockGetLeaguesByGameworld.mockResolvedValue(emptyLeagues);

    const event = {
      Records: [{ body: JSON.stringify({ gameworldId: 'test-gameworld' }) }],
      context: context,
    };
    await handler(event, context);

    expect(mockUpdateTeamLeagues).not.toHaveBeenCalled();
    expect(logger.warn).toHaveBeenCalledWith('No teams found for gameworld', {
      gameworldId: 'test-gameworld',
    });
  });
});
