import { DBLeagueTable } from '@/model/leagueTable.js';
import { DBTeam } from '@/model/team.js';
import { createNumTeams, createTeam } from '@/testing/teamsTestUtils.js';
import { LeagueProcessor } from './LeagueProcessor.js';

describe('LeagueProcessor', () => {
  describe('groupTeamsByLeague', () => {
    it('should group teams by their league ID', () => {
      const teams: DBTeam[] = [
        { teamId: '1', leagueId: 'L1', teamName: 'Team 1' } as DBTeam,
        { teamId: '2', leagueId: 'L1', teamName: 'Team 2' } as DBTeam,
        { teamId: '3', leagueId: 'L2', teamName: 'Team 3' } as DBTeam,
      ];

      const result = LeagueProcessor.groupTeamsByLeague(teams);

      expect(result.get('L1')).toHaveLength(2);
      expect(result.get('L2')).toHaveLength(1);
    });
  });

  describe('sortTeamsInLeague', () => {
    it('should sort teams correctly by multiple criteria', () => {
      const teams: DBTeam[] = [
        // Same points, different goal difference
        createTeam('team1', 'league1', 70, 50, 40, 20),
        createTeam('team2', 'league1', 70, 40, 20, 15),
        // Same points and goal difference, different goals scored
        createTeam('team3', 'league1', 65, 30, 20, 20),
        createTeam('team4', 'league1', 65, 35, 25, 25),
        // Same everything, alphabetical order
        createTeam('team5', 'league1', 60, 25, 25, 15),
        createTeam('team6', 'league1', 60, 25, 25, 15),
        // Additional teams to make up 15
        createTeam('team7', 'league1', 55, 40, 10, 16),
        createTeam('team8', 'league1', 50, 35, 10, 14),
        createTeam('team9', 'league1', 45, 30, 10, 13),
        createTeam('team10', 'league1', 40, 28, 10, 11),
        createTeam('team11', 'league1', 35, 25, 10, 9),
        createTeam('team12', 'league1', 30, 22, 10, 8),
        createTeam('team13', 'league1', 25, 20, 10, 6),
        createTeam('team14', 'league1', 20, 18, 10, 5),
        createTeam('team15', 'league1', 15, 15, 10, 4),
      ];

      const result = LeagueProcessor.sortTeamsInLeague(teams);
      expect(result).toMatchSnapshot();
    });
  });

  describe('validateTeamCounts', () => {
    const leagueMap = new Map<string, DBLeagueTable>([
      [
        'L1',
        {
          id: 'L1',
          gameworldId: 'test-gameworld',
          tier: 1,
          parentLeagueId: '',
          childLeagueIds: ['L2'],
          rules: { promotion: 0, relegation: 1, teamCount: 15 },
        },
      ],
      [
        'L2',
        {
          id: 'L2',
          gameworldId: 'test-gameworld',
          tier: 2,
          parentLeagueId: 'L1',
          childLeagueIds: [],
          rules: { promotion: 1, relegation: 0, teamCount: 15 },
        },
      ],
      [
        'L3',
        {
          id: 'L3',
          gameworldId: 'test-gameworld',
          tier: 3,
          parentLeagueId: 'L2',
          childLeagueIds: [],
          rules: { promotion: 1, relegation: 0, teamCount: 15 },
        },
      ],
    ]);

    it('should return true when all leagues have the required number of teams', () => {
      const teamCounts = {
        L1: 15,
        L2: 15,
        L3: 15,
      };

      expect(LeagueProcessor.validateTeamCounts(teamCounts, leagueMap)).toBe(true);
    });

    it('should return false when any league has incorrect number of teams', () => {
      const teamCounts = {
        L1: 15,
        L2: 14,
        L3: 15,
      };

      expect(LeagueProcessor.validateTeamCounts(teamCounts, leagueMap)).toBe(false);
    });
  });

  describe('processPromotionsAndRelegations', () => {
    it('should process promotions and relegations correctly', () => {
      const leagues: DBLeagueTable[] = [
        {
          id: 'L1',
          gameworldId: 'test-gameworld',
          tier: 1,
          childLeagueIds: ['L2'],
          rules: { promotion: 0, relegation: 1, teamCount: 3 },
        } as DBLeagueTable,
        {
          id: 'L2',
          gameworldId: 'test-gameworld',
          tier: 2,
          parentLeagueId: 'L1',
          childLeagueIds: [],
          rules: { promotion: 1, relegation: 0, teamCount: 3 },
        } as DBLeagueTable,
      ];

      // pre sorted leagues.
      const teams = new Map<string, DBTeam[]>([
        [
          'L1',
          [
            createTeam('T1', 'L1', 10, 10, 5, 3),
            createTeam('T2', 'L1', 5, 5, 10, 1),
            createTeam('T3', 'L1', 0, 0, 5, 0),
          ],
        ],
        [
          'L2',
          [
            createTeam('T6', 'L2', 12, 15, 5, 4),
            createTeam('T4', 'L2', 10, 10, 5, 3),
            createTeam('T5', 'L2', 8, 8, 5, 2),
          ],
        ],
      ]);

      const result = LeagueProcessor.processPromotionsAndRelegations(teams, leagues);

      expect(result).toHaveLength(2);
      expect(result).toEqual([
        { fromLeagueId: 'L2', teamId: 'T6', toLeagueId: 'L1' },
        { fromLeagueId: 'L1', teamId: 'T3', toLeagueId: 'L2' },
      ]);
    });

    it('should throw error when resulting team distribution is invalid', () => {
      const leagues: DBLeagueTable[] = [
        {
          id: 'L1',
          gameworldId: 'test-gameworld',
          tier: 1,
          childLeagueIds: ['L2'],
          rules: { promotion: 0, relegation: 2 }, // Invalid configuration
        } as any,
        {
          id: 'L2',
          gameworldId: 'test-gameworld',
          tier: 2,
          parentLeagueId: 'L1',
          childLeagueIds: [],
          rules: { promotion: 1, relegation: 0, teamCount: 2 },
        } as DBLeagueTable,
      ];

      const teams = new Map<string, DBTeam[]>([
        ['L1', [{ teamId: 'T1' } as DBTeam, { teamId: 'T2' } as DBTeam]],
        ['L2', [{ teamId: 'T3' } as DBTeam]],
      ]);

      expect(() => {
        LeagueProcessor.processPromotionsAndRelegations(teams, leagues);
      }).toThrow('Invalid team distribution after promotions/relegations');
    });

    it('should distribute relegations evenly across multiple child leagues', () => {
      const leagues: DBLeagueTable[] = [
        {
          id: 'L1',
          gameworldId: 'test-gameworld',
          tier: 1,
          childLeagueIds: ['L2', 'L3', 'L4'],
          rules: { promotion: 0, relegation: 6, teamCount: 15 },
        } as DBLeagueTable,
        {
          id: 'L2',
          gameworldId: 'test-gameworld',
          tier: 2,
          parentLeagueId: 'L1',
          childLeagueIds: [],
          rules: { promotion: 2, relegation: 0, teamCount: 15 },
        } as DBLeagueTable,
        {
          id: 'L3',
          gameworldId: 'test-gameworld',
          tier: 2,
          parentLeagueId: 'L1',
          childLeagueIds: [],
          rules: { promotion: 2, relegation: 0, teamCount: 15 },
        } as DBLeagueTable,
        {
          id: 'L4',
          gameworldId: 'test-gameworld',
          tier: 2,
          parentLeagueId: 'L1',
          childLeagueIds: [],
          rules: { promotion: 2, relegation: 0, teamCount: 15 },
        } as DBLeagueTable,
      ];

      // Pre-sorted teams - bottom 6 teams from L1 will be relegated
      const teams = new Map<string, DBTeam[]>([
        ['L1', createNumTeams(15, 'L1')],
        ['L2', createNumTeams(15, 'L2')],
        ['L3', createNumTeams(15, 'L3')],
        ['L4', createNumTeams(15, 'L4')],
      ]);

      const result = LeagueProcessor.processPromotionsAndRelegations(teams, leagues);

      // Expect 9 movements: 6 relegations (2 teams to each child league) + 6 promotions (1 from each child league)
      expect(result).toHaveLength(12);

      // Verify relegations are distributed evenly
      const relegationsToL2 = result.filter(
        (m) => m.fromLeagueId === 'L1' && m.toLeagueId === 'L2'
      );
      const relegationsToL3 = result.filter(
        (m) => m.fromLeagueId === 'L1' && m.toLeagueId === 'L3'
      );
      const relegationsToL4 = result.filter(
        (m) => m.fromLeagueId === 'L1' && m.toLeagueId === 'L4'
      );

      expect(relegationsToL2).toHaveLength(2);
      expect(relegationsToL3).toHaveLength(2);
      expect(relegationsToL4).toHaveLength(2);

      // Verify the worst teams are relegated
      expect(relegationsToL2.map((m) => m.teamId)).toEqual(['team10', 'team11']);
      expect(relegationsToL3.map((m) => m.teamId)).toEqual(['team12', 'team13']);
      expect(relegationsToL4.map((m) => m.teamId)).toEqual(['team14', 'team15']);

      // Verify promotions
      const promotions = result.filter((m) => m.toLeagueId === 'L1');
      expect(promotions).toHaveLength(6);
      expect(promotions.map((m) => m.teamId)).toEqual([
        'team1',
        'team2',
        'team1',
        'team2',
        'team1',
        'team2',
      ]);
    });
  });
});
