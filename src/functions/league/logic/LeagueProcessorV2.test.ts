import { League } from '@/entities/League.js';
import { LeagueRules } from '@/entities/LeagueRules.js';
import { Team } from '@/entities/Team.js';
import { LeagueProcessor, TeamMovement } from './LeagueProcessorV2.js';

// Import jest explicitly
import * as jest from 'jest';

describe('LeagueProcessorV2', () => {
  describe('groupTeamsByLeague', () => {
    it('should group teams by their league ID', () => {
      // Create test teams with proper league references
      const team1 = createTeam('1', 'L1', 0, 0, 0, 0);
      const team2 = createTeam('2', 'L1', 0, 0, 0, 0);
      const team3 = createTeam('3', 'L2', 0, 0, 0, 0);

      const teams: Team[] = [team1, team2, team3];

      const result = LeagueProcessor.groupTeamsByLeague(teams);

      expect(result.get('L1')).toHaveLength(2);
      expect(result.get('L2')).toHaveLength(1);

      // Verify the teams are correctly grouped
      expect(result.get('L1')).toContainEqual(expect.objectContaining({ teamId: '1' }));
      expect(result.get('L1')).toContainEqual(expect.objectContaining({ teamId: '2' }));
      expect(result.get('L2')).toContainEqual(expect.objectContaining({ teamId: '3' }));
    });

    it('should handle empty teams array', () => {
      const teams: Team[] = [];
      const result = LeagueProcessor.groupTeamsByLeague(teams);

      expect(result.size).toBe(0);
    });

    it('should handle teams with the same league ID', () => {
      // Create multiple teams in the same league
      const teams: Team[] = [
        createTeam('1', 'L1', 0, 0, 0, 0),
        createTeam('2', 'L1', 0, 0, 0, 0),
        createTeam('3', 'L1', 0, 0, 0, 0),
        createTeam('4', 'L1', 0, 0, 0, 0),
        createTeam('5', 'L1', 0, 0, 0, 0),
      ];

      const result = LeagueProcessor.groupTeamsByLeague(teams);

      expect(result.size).toBe(1);
      expect(result.get('L1')).toHaveLength(5);
    });
  });

  describe('sortTeamsInLeague', () => {
    it('should sort teams correctly by points (highest first)', () => {
      const team1 = createTeam('team1', 'league1', 70, 30, 20, 20); // 70 points
      const team2 = createTeam('team2', 'league1', 65, 30, 20, 20); // 65 points
      const team3 = createTeam('team3', 'league1', 60, 30, 20, 20); // 60 points

      const teams: Team[] = [team3, team1, team2]; // Unsorted order

      const result = LeagueProcessor.sortTeamsInLeague(teams);

      // Teams should be sorted by points (highest first)
      expect(result[0]!.teamId).toBe('team1'); // 70 points
      expect(result[1]!.teamId).toBe('team2'); // 65 points
      expect(result[2]!.teamId).toBe('team3'); // 60 points
    });

    it('should sort teams by goal difference when points are equal', () => {
      const team1 = createTeam('team1', 'league1', 70, 50, 40, 20); // GD: 10
      const team2 = createTeam('team2', 'league1', 70, 40, 20, 20); // GD: 20
      const team3 = createTeam('team3', 'league1', 70, 30, 30, 20); // GD: 0

      const teams: Team[] = [team3, team1, team2]; // Unsorted order

      const result = LeagueProcessor.sortTeamsInLeague(teams);

      // Teams should be sorted by goal difference when points are equal
      expect(result[0]!.teamId).toBe('team2'); // GD: 20
      expect(result[1]!.teamId).toBe('team1'); // GD: 10
      expect(result[2]!.teamId).toBe('team3'); // GD: 0
    });

    it('should sort teams by goals scored when points and goal difference are equal', () => {
      const team1 = createTeam('team1', 'league1', 70, 40, 30, 20); // GD: 10, GF: 40
      const team2 = createTeam('team2', 'league1', 70, 30, 20, 20); // GD: 10, GF: 30
      const team3 = createTeam('team3', 'league1', 70, 50, 40, 20); // GD: 10, GF: 50

      const teams: Team[] = [team2, team1, team3]; // Unsorted order

      const result = LeagueProcessor.sortTeamsInLeague(teams);

      // Teams should be sorted by goals scored when points and goal difference are equal
      expect(result[0]!.teamId).toBe('team3'); // GF: 50
      expect(result[1]!.teamId).toBe('team1'); // GF: 40
      expect(result[2]!.teamId).toBe('team2'); // GF: 30
    });

    it('should sort teams by wins when points, goal difference, and goals scored are equal', () => {
      const team1 = createTeam('team1', 'league1', 70, 40, 30, 20); // 20 wins
      const team2 = createTeam('team2', 'league1', 70, 40, 30, 23); // 23 wins
      const team3 = createTeam('team3', 'league1', 70, 40, 30, 18); // 18 wins

      const teams: Team[] = [team3, team1, team2]; // Unsorted order

      const result = LeagueProcessor.sortTeamsInLeague(teams);

      // Teams should be sorted by wins when other criteria are equal
      expect(result[0]!.teamId).toBe('team2'); // 23 wins
      expect(result[1]!.teamId).toBe('team1'); // 20 wins
      expect(result[2]!.teamId).toBe('team3'); // 18 wins
    });

    it('should sort teams alphabetically when all other criteria are equal', () => {
      const team1 = createTeam('team1', 'league1', 70, 40, 30, 20);
      team1.teamName = 'Barcelona';

      const team2 = createTeam('team2', 'league1', 70, 40, 30, 20);
      team2.teamName = 'Arsenal';

      const team3 = createTeam('team3', 'league1', 70, 40, 30, 20);
      team3.teamName = 'Chelsea';

      const teams: Team[] = [team3, team1, team2]; // Unsorted order

      const result = LeagueProcessor.sortTeamsInLeague(teams);

      // Teams should be sorted alphabetically when all other criteria are equal
      expect(result[0]!.teamId).toBe('team2'); // Arsenal
      expect(result[1]!.teamId).toBe('team1'); // Barcelona
      expect(result[2]!.teamId).toBe('team3'); // Chelsea
    });

    it('should handle complex sorting with multiple criteria', () => {
      const team1 = createTeam('team1', 'league1', 70, 50, 40, 20); // 70 pts, GD: 10
      const team2 = createTeam('team2', 'league1', 70, 40, 20, 15); // 70 pts, GD: 20
      const team3 = createTeam('team3', 'league1', 65, 30, 20, 20); // 65 pts, GD: 10, GF: 30
      const team4 = createTeam('team4', 'league1', 65, 35, 25, 25); // 65 pts, GD: 10, GF: 35
      const team5 = createTeam('team5', 'league1', 60, 25, 25, 15); // 60 pts, GD: 0
      const team6 = createTeam('team6', 'league1', 60, 25, 25, 15); // 60 pts, GD: 0
      team5.teamName = 'Team A';
      team6.teamName = 'Team B';

      const teams: Team[] = [
        team1, team2, team3, team4, team5, team6
      ];

      const result = LeagueProcessor.sortTeamsInLeague(teams);

      // team2 should be first because it has better goal difference
      expect(result[0]!.teamId).toBe('team2');
      expect(result[1]!.teamId).toBe('team1');
      // team4 should be third because it has more goals scored
      expect(result[2]!.teamId).toBe('team4');
      expect(result[3]!.teamId).toBe('team3');
      // team5 and team6 should be sorted alphabetically
      expect(result[4]!.teamId).toBe('team5'); // Team A
      expect(result[5]!.teamId).toBe('team6'); // Team B
    });

    it('should handle empty teams array', () => {
      const teams: Team[] = [];
      const result = LeagueProcessor.sortTeamsInLeague(teams);

      expect(result).toEqual([]);
    });
  });

  describe('getTeamStandings', () => {
    it('should return correct team standings', () => {
      const team = createTeam('team1', 'league1', 30, 40, 25, 10);
      team.teamName = 'Test Team';

      const standings = LeagueProcessor.getTeamStandings(team);

      expect(standings).toEqual({
        teamName: 'Test Team',
        points: 30,
        goalDiff: 15, // 40 - 25
        goalsFor: 40,
        wins: 10
      });
    });

    it('should handle negative goal difference', () => {
      const team = createTeam('team1', 'league1', 20, 15, 30, 6);
      team.teamName = 'Test Team';

      const standings = LeagueProcessor.getTeamStandings(team);

      expect(standings).toEqual({
        teamName: 'Test Team',
        points: 20,
        goalDiff: -15, // 15 - 30
        goalsFor: 15,
        wins: 6
      });
    });
  });

  describe('validateTeamCounts', () => {
    it('should return true when all leagues have the correct number of teams', () => {
      const league1 = createLeague('league1', 1, 0, 0, 3);
      const league2 = createLeague('league2', 2, 1, 0, 2);

      const leagueMap = new Map<string, League>();
      leagueMap.set('league1', league1);
      leagueMap.set('league2', league2);

      const teamCounts = {
        'league1': 3,
        'league2': 2
      };

      const result = LeagueProcessor.validateTeamCounts(teamCounts, leagueMap);
      expect(result).toBe(true);
    });

    it('should return false when any league has an incorrect number of teams', () => {
      const league1 = createLeague('league1', 1, 0, 0, 3);
      const league2 = createLeague('league2', 2, 1, 0, 2);

      const leagueMap = new Map<string, League>();
      leagueMap.set('league1', league1);
      leagueMap.set('league2', league2);

      const teamCounts = {
        'league1': 3,
        'league2': 3 // Should be 2
      };

      const result = LeagueProcessor.validateTeamCounts(teamCounts, leagueMap);
      expect(result).toBe(false);
    });

    it('should return false when a league is missing from the map', () => {
      const league1 = createLeague('league1', 1, 0, 0, 3);

      const leagueMap = new Map<string, League>();
      leagueMap.set('league1', league1);

      const teamCounts = {
        'league1': 3,
        'league2': 2 // Not in the map
      };

      const result = LeagueProcessor.validateTeamCounts(teamCounts, leagueMap);
      expect(result).toBe(false);
    });

    it('should return false when a league has missing league rules', () => {
      const league1 = createLeague('league1', 1, 0, 0, 3);

      // Create a league without rules
      const league2 = new League();
      league2.id = 'league2';
      league2.tier = 2;
      league2.name = 'League 2';
      league2.gameworldId = 'test-gameworld';
      // Intentionally not setting leagueRules

      const leagueMap = new Map<string, League>();
      leagueMap.set('league1', league1);
      leagueMap.set('league2', league2);

      const teamCounts = {
        'league1': 3,
        'league2': 2
      };

      const result = LeagueProcessor.validateTeamCounts(teamCounts, leagueMap);
      expect(result).toBe(false);
    });
  });

  describe('processPromotionsAndRelegations', () => {
    // Save the original method
    const originalValidateTeamCounts = LeagueProcessor.validateTeamCounts;

    // Replace with a mock implementation before tests
    beforeEach(() => {
      // Override the validateTeamCounts method to always return true
      LeagueProcessor.validateTeamCounts = () => true;
    });

    // Restore the original method after tests
    afterEach(() => {
      LeagueProcessor.validateTeamCounts = originalValidateTeamCounts;
    });

    it('should return empty array when no leagues are provided', () => {
      const sortedLeagues = new Map<string, Team[]>();
      const leagues: League[] = [];

      const movements = LeagueProcessor.processPromotionsAndRelegations(sortedLeagues, leagues);

      expect(movements).toEqual([]);
    });

    it('should return empty array when no teams are provided', () => {
      const league = createLeague('league1', 1, 0, 0, 0);
      const leagues = [league];
      const sortedLeagues = new Map<string, Team[]>();

      const movements = LeagueProcessor.processPromotionsAndRelegations(sortedLeagues, leagues);

      expect(movements).toEqual([]);
    });

    it('should process promotions and relegations correctly', () => {
      // Create leagues with proper structure
      const parentLeague = createLeague('parent', 1, 0, 2, 5); // 5 teams total
      const childLeague = createLeague('child', 2, 1, 0, 5); // 5 teams total

      // Set up parent-child relationship
      Object.defineProperty(childLeague, 'parentLeague', {
        get: () => ({ id: 'parent' })
      });

      // Mock the leagueChildren collection
      parentLeague.leagueChildren = {
        getItems: () => [childLeague],
        contains: () => true,
        add: () => {}
      } as any;

      const leagues = [parentLeague, childLeague];

      // Create teams in the child league - 5 teams total
      const team1 = createTeam('team1', 'child', 30, 20, 10, 10);
      const team2 = createTeam('team2', 'child', 25, 15, 10, 8);
      const team3 = createTeam('team3', 'child', 20, 10, 10, 6);
      const team4 = createTeam('team4', 'child', 15, 8, 10, 5);
      const team5 = createTeam('team5', 'child', 10, 5, 10, 3);

      // Create teams in the parent league - 5 teams total
      const team6 = createTeam('team6', 'parent', 30, 20, 10, 10);
      const team7 = createTeam('team7', 'parent', 25, 15, 10, 8);
      const team8 = createTeam('team8', 'parent', 20, 10, 10, 6);
      const team9 = createTeam('team9', 'parent', 15, 8, 10, 5);
      const team10 = createTeam('team10', 'parent', 10, 5, 10, 3);

      // Set up the sorted leagues map
      const sortedLeagues = new Map<string, Team[]>();
      sortedLeagues.set('child', [team1, team2, team3, team4, team5]);
      sortedLeagues.set('parent', [team6, team7, team8, team9, team10]);

      // Process promotions and relegations
      const movements = LeagueProcessor.processPromotionsAndRelegations(sortedLeagues, leagues);

      // Verify the movements
      expect(movements.length).toBe(3); // 1 promotion, 2 relegations

      // Check promotion
      const promotion = movements.find(
        (m) => m.fromLeagueId === 'child' && m.toLeagueId === 'parent'
      );
      expect(promotion).toBeDefined();
      expect(promotion?.teamId).toBe('team1');

      // Check relegations
      const relegations = movements.filter(
        (m) => m.fromLeagueId === 'parent' && m.toLeagueId === 'child'
      );
      expect(relegations.length).toBe(2);
      expect(relegations.map((r) => r.teamId)).toContain('team9');
      expect(relegations.map((r) => r.teamId)).toContain('team10');
    });

    it('should handle multiple child leagues for relegation distribution', () => {
      // Create a parent league with 2 child leagues
      const parentLeague = createLeague('parent', 1, 0, 4, 6); // 6 teams, 4 relegation spots
      const childLeague1 = createLeague('child1', 2, 1, 0, 5); // 5 teams, 1 promotion spot
      const childLeague2 = createLeague('child2', 2, 1, 0, 5); // 5 teams, 1 promotion spot

      // Set up parent-child relationships
      Object.defineProperty(childLeague1, 'parentLeague', {
        get: () => ({ id: 'parent' })
      });
      Object.defineProperty(childLeague2, 'parentLeague', {
        get: () => ({ id: 'parent' })
      });

      // Mock the leagueChildren collection
      parentLeague.leagueChildren = {
        getItems: () => [childLeague1, childLeague2],
        contains: () => true,
        add: () => {}
      } as any;

      const leagues = [parentLeague, childLeague1, childLeague2];

      // Create teams in parent league
      const parentTeams = [
        createTeam('p1', 'parent', 30, 20, 10, 10),
        createTeam('p2', 'parent', 25, 15, 10, 8),
        createTeam('p3', 'parent', 20, 10, 10, 6),
        createTeam('p4', 'parent', 15, 8, 10, 5), // Relegated
        createTeam('p5', 'parent', 10, 5, 10, 3), // Relegated
        createTeam('p6', 'parent', 5, 3, 10, 1),  // Relegated
      ];

      // Create teams in child leagues
      const child1Teams = [
        createTeam('c1-1', 'child1', 30, 20, 10, 10), // Promoted
        createTeam('c1-2', 'child1', 25, 15, 10, 8),
        createTeam('c1-3', 'child1', 20, 10, 10, 6),
        createTeam('c1-4', 'child1', 15, 8, 10, 5),
        createTeam('c1-5', 'child1', 10, 5, 10, 3),
      ];

      const child2Teams = [
        createTeam('c2-1', 'child2', 30, 20, 10, 10), // Promoted
        createTeam('c2-2', 'child2', 25, 15, 10, 8),
        createTeam('c2-3', 'child2', 20, 10, 10, 6),
        createTeam('c2-4', 'child2', 15, 8, 10, 5),
        createTeam('c2-5', 'child2', 10, 5, 10, 3),
      ];

      // Set up the sorted leagues map
      const sortedLeagues = new Map<string, Team[]>();
      sortedLeagues.set('parent', parentTeams);
      sortedLeagues.set('child1', child1Teams);
      sortedLeagues.set('child2', child2Teams);

      // Process promotions and relegations
      const movements = LeagueProcessor.processPromotionsAndRelegations(sortedLeagues, leagues);

      // Verify the movements
      expect(movements.length).toBe(6); // 2 promotions, 4 relegations

      // Check promotions
      const promotions = movements.filter(
        (m) => m.toLeagueId === 'parent'
      );
      expect(promotions.length).toBe(2);
      expect(promotions.map(p => p.teamId)).toContain('c1-1');
      expect(promotions.map(p => p.teamId)).toContain('c2-1');

      // Check relegations - should be evenly distributed
      const relegationsToChild1 = movements.filter(
        (m) => m.fromLeagueId === 'parent' && m.toLeagueId === 'child1'
      );
      const relegationsToChild2 = movements.filter(
        (m) => m.fromLeagueId === 'parent' && m.toLeagueId === 'child2'
      );

      expect(relegationsToChild1.length).toBe(2);
      expect(relegationsToChild2.length).toBe(2);
    });

    it('should validate team counts after processing', () => {
      // Temporarily restore the original validateTeamCounts for this test
      const tempValidateTeamCounts = LeagueProcessor.validateTeamCounts;
      LeagueProcessor.validateTeamCounts = originalValidateTeamCounts;

      try {
        // Create a simple test case
        const league1 = createLeague('league1', 1, 0, 0, 2); // 2 teams, no promotions/relegations
        const leagues = [league1];

        const team1 = createTeam('team1', 'league1', 30, 20, 10, 10);
        const team2 = createTeam('team2', 'league1', 25, 15, 10, 8);

        const sortedLeagues = new Map<string, Team[]>();
        sortedLeagues.set('league1', [team1, team2]);

        // Process should succeed with no movements
        const movements = LeagueProcessor.processPromotionsAndRelegations(sortedLeagues, leagues);
        expect(movements).toEqual([]);
      } finally {
        // Restore the mock implementation
        LeagueProcessor.validateTeamCounts = tempValidateTeamCounts;
      }
    });

    it('should throw an error if team distribution is invalid after processing', () => {
      // Temporarily override validateTeamCounts to return false for this test
      const tempValidateTeamCounts = LeagueProcessor.validateTeamCounts;
      LeagueProcessor.validateTeamCounts = () => false;

      try {
        // Create a simple test case
        const league1 = createLeague('league1', 1, 0, 0, 2); // 2 teams
        const leagues = [league1];

        const team1 = createTeam('team1', 'league1', 30, 20, 10, 10);
        const team2 = createTeam('team2', 'league1', 25, 15, 10, 8);

        const sortedLeagues = new Map<string, Team[]>();
        sortedLeagues.set('league1', [team1, team2]);

        // Process should throw an error
        expect(() => {
          LeagueProcessor.processPromotionsAndRelegations(sortedLeagues, leagues);
        }).toThrow('Invalid team distribution after promotions/relegations');
      } finally {
        // Restore the original method
        LeagueProcessor.validateTeamCounts = tempValidateTeamCounts;
      }
    });

    it('should handle leagues with no parent or child leagues', () => {
      // Create a standalone league
      const standaloneLeague = createLeague('standalone', 1, 0, 0, 3); // 3 teams, no promotions/relegations
      const leagues = [standaloneLeague];

      const team1 = createTeam('team1', 'standalone', 30, 20, 10, 10);
      const team2 = createTeam('team2', 'standalone', 25, 15, 10, 8);
      const team3 = createTeam('team3', 'standalone', 20, 10, 10, 6);

      const sortedLeagues = new Map<string, Team[]>();
      sortedLeagues.set('standalone', [team1, team2, team3]);

      // Process should succeed with no movements
      const movements = LeagueProcessor.processPromotionsAndRelegations(sortedLeagues, leagues);
      expect(movements).toEqual([]);
    });

    it('should handle invalid relegation configuration', () => {
      // Create a league with an odd number of relegation spots and 2 child leagues
      const parentLeague = createLeague('parent', 1, 0, 3, 5); // 5 teams, 3 relegation spots
      const childLeague1 = createLeague('child1', 2, 1, 0, 5); // 5 teams, 1 promotion spot
      const childLeague2 = createLeague('child2', 2, 1, 0, 5); // 5 teams, 1 promotion spot

      // Set up parent-child relationships
      Object.defineProperty(childLeague1, 'parentLeague', {
        get: () => ({ id: 'parent' })
      });
      Object.defineProperty(childLeague2, 'parentLeague', {
        get: () => ({ id: 'parent' })
      });

      // Mock the leagueChildren collection
      parentLeague.leagueChildren = {
        getItems: () => [childLeague1, childLeague2],
        contains: () => true,
        add: () => {}
      } as any;

      const leagues = [parentLeague, childLeague1, childLeague2];

      // Create teams in parent league
      const parentTeams = [
        createTeam('p1', 'parent', 30, 20, 10, 10),
        createTeam('p2', 'parent', 25, 15, 10, 8),
        createTeam('p3', 'parent', 20, 10, 10, 6),
        createTeam('p4', 'parent', 15, 8, 10, 5),
        createTeam('p5', 'parent', 10, 5, 10, 3),
      ];

      // Create teams in child leagues
      const child1Teams = [
        createTeam('c1-1', 'child1', 30, 20, 10, 10),
        createTeam('c1-2', 'child1', 25, 15, 10, 8),
        createTeam('c1-3', 'child1', 20, 10, 10, 6),
        createTeam('c1-4', 'child1', 15, 8, 10, 5),
        createTeam('c1-5', 'child1', 10, 5, 10, 3),
      ];

      const child2Teams = [
        createTeam('c2-1', 'child2', 30, 20, 10, 10),
        createTeam('c2-2', 'child2', 25, 15, 10, 8),
        createTeam('c2-3', 'child2', 20, 10, 10, 6),
        createTeam('c2-4', 'child2', 15, 8, 10, 5),
        createTeam('c2-5', 'child2', 10, 5, 10, 3),
      ];

      // Set up the sorted leagues map
      const sortedLeagues = new Map<string, Team[]>();
      sortedLeagues.set('parent', parentTeams);
      sortedLeagues.set('child1', child1Teams);
      sortedLeagues.set('child2', child2Teams);

      // Process should still work but skip the invalid relegation configuration
      const movements = LeagueProcessor.processPromotionsAndRelegations(sortedLeagues, leagues);

      // Should only have promotions, no relegations due to invalid config
      expect(movements.length).toBe(2); // Only 2 promotions

      // Check that we only have promotions
      const promotions = movements.filter(m => m.toLeagueId === 'parent');
      expect(promotions.length).toBe(2);

      // No relegations should be processed
      const relegations = movements.filter(m => m.fromLeagueId === 'parent');
      expect(relegations.length).toBe(0);
    });
  });
});

// Helper functions to create test data

/**
 * Creates a Team entity for testing
 */
function createTeam(
  teamId: string,
  leagueId: string,
  points: number,
  goalsFor: number,
  goalsAgainst: number,
  wins: number
): Team {
  const team = new Team();
  team.teamId = teamId;
  team.teamName = `Team ${teamId}`;
  team.points = points;
  team.goalsFor = goalsFor;
  team.goalsAgainst = goalsAgainst;
  team.wins = wins;
  team.draws = points - wins * 3;
  team.losses = 0;
  team.played = 0;
  team.gameworldId = 'test-gameworld';
  team.tier = 1;
  team.balance = 300000;
  team.selectionOrder = [];

  // For testing purposes, we'll mock the league property
  // by directly setting the id property that LeagueProcessor uses
  Object.defineProperty(team, 'league', {
    get: () => ({ id: leagueId })
  });

  return team;
}

/**
 * Creates a League entity with LeagueRules for testing
 */
function createLeague(
  id: string,
  tier: number,
  promotionSpots: number,
  relegationSpots: number,
  teamCount: number
): League {
  const league = new League();
  league.id = id;
  league.tier = tier;
  league.name = `League ${id}`;
  league.gameworldId = 'test-gameworld';

  // Create league rules
  const rules = new LeagueRules();
  rules.promotionSpots = promotionSpots;
  rules.relegationSpots = relegationSpots;
  rules.teamCount = teamCount;
  rules.league = league;

  // Set the rules on the league
  league.leagueRules = rules;

  return league;
}
