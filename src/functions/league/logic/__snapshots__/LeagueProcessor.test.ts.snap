// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`LeagueProcessor sortTeamsInLeague should sort teams correctly by multiple criteria 1`] = `
[
  {
    "gameworldId": "test-gameworld",
    "leagueId": "league1",
    "selectionOrder": [],
    "standings": {
      "draws": 25,
      "goalsAgainst": 20,
      "goalsFor": 40,
      "losses": 0,
      "played": 0,
      "points": 70,
      "wins": 15,
    },
    "teamId": "team2",
    "teamName": "Test Team",
  },
  {
    "gameworldId": "test-gameworld",
    "leagueId": "league1",
    "selectionOrder": [],
    "standings": {
      "draws": 10,
      "goalsAgainst": 40,
      "goalsFor": 50,
      "losses": 0,
      "played": 0,
      "points": 70,
      "wins": 20,
    },
    "teamId": "team1",
    "teamName": "Test Team",
  },
  {
    "gameworldId": "test-gameworld",
    "leagueId": "league1",
    "selectionOrder": [],
    "standings": {
      "draws": -10,
      "goalsAgainst": 25,
      "goalsFor": 35,
      "losses": 0,
      "played": 0,
      "points": 65,
      "wins": 25,
    },
    "teamId": "team4",
    "teamName": "Test Team",
  },
  {
    "gameworldId": "test-gameworld",
    "leagueId": "league1",
    "selectionOrder": [],
    "standings": {
      "draws": 5,
      "goalsAgainst": 20,
      "goalsFor": 30,
      "losses": 0,
      "played": 0,
      "points": 65,
      "wins": 20,
    },
    "teamId": "team3",
    "teamName": "Test Team",
  },
  {
    "gameworldId": "test-gameworld",
    "leagueId": "league1",
    "selectionOrder": [],
    "standings": {
      "draws": 15,
      "goalsAgainst": 25,
      "goalsFor": 25,
      "losses": 0,
      "played": 0,
      "points": 60,
      "wins": 15,
    },
    "teamId": "team5",
    "teamName": "Test Team",
  },
  {
    "gameworldId": "test-gameworld",
    "leagueId": "league1",
    "selectionOrder": [],
    "standings": {
      "draws": 15,
      "goalsAgainst": 25,
      "goalsFor": 25,
      "losses": 0,
      "played": 0,
      "points": 60,
      "wins": 15,
    },
    "teamId": "team6",
    "teamName": "Test Team",
  },
  {
    "gameworldId": "test-gameworld",
    "leagueId": "league1",
    "selectionOrder": [],
    "standings": {
      "draws": 7,
      "goalsAgainst": 10,
      "goalsFor": 40,
      "losses": 0,
      "played": 0,
      "points": 55,
      "wins": 16,
    },
    "teamId": "team7",
    "teamName": "Test Team",
  },
  {
    "gameworldId": "test-gameworld",
    "leagueId": "league1",
    "selectionOrder": [],
    "standings": {
      "draws": 8,
      "goalsAgainst": 10,
      "goalsFor": 35,
      "losses": 0,
      "played": 0,
      "points": 50,
      "wins": 14,
    },
    "teamId": "team8",
    "teamName": "Test Team",
  },
  {
    "gameworldId": "test-gameworld",
    "leagueId": "league1",
    "selectionOrder": [],
    "standings": {
      "draws": 6,
      "goalsAgainst": 10,
      "goalsFor": 30,
      "losses": 0,
      "played": 0,
      "points": 45,
      "wins": 13,
    },
    "teamId": "team9",
    "teamName": "Test Team",
  },
  {
    "gameworldId": "test-gameworld",
    "leagueId": "league1",
    "selectionOrder": [],
    "standings": {
      "draws": 7,
      "goalsAgainst": 10,
      "goalsFor": 28,
      "losses": 0,
      "played": 0,
      "points": 40,
      "wins": 11,
    },
    "teamId": "team10",
    "teamName": "Test Team",
  },
  {
    "gameworldId": "test-gameworld",
    "leagueId": "league1",
    "selectionOrder": [],
    "standings": {
      "draws": 8,
      "goalsAgainst": 10,
      "goalsFor": 25,
      "losses": 0,
      "played": 0,
      "points": 35,
      "wins": 9,
    },
    "teamId": "team11",
    "teamName": "Test Team",
  },
  {
    "gameworldId": "test-gameworld",
    "leagueId": "league1",
    "selectionOrder": [],
    "standings": {
      "draws": 6,
      "goalsAgainst": 10,
      "goalsFor": 22,
      "losses": 0,
      "played": 0,
      "points": 30,
      "wins": 8,
    },
    "teamId": "team12",
    "teamName": "Test Team",
  },
  {
    "gameworldId": "test-gameworld",
    "leagueId": "league1",
    "selectionOrder": [],
    "standings": {
      "draws": 7,
      "goalsAgainst": 10,
      "goalsFor": 20,
      "losses": 0,
      "played": 0,
      "points": 25,
      "wins": 6,
    },
    "teamId": "team13",
    "teamName": "Test Team",
  },
  {
    "gameworldId": "test-gameworld",
    "leagueId": "league1",
    "selectionOrder": [],
    "standings": {
      "draws": 5,
      "goalsAgainst": 10,
      "goalsFor": 18,
      "losses": 0,
      "played": 0,
      "points": 20,
      "wins": 5,
    },
    "teamId": "team14",
    "teamName": "Test Team",
  },
  {
    "gameworldId": "test-gameworld",
    "leagueId": "league1",
    "selectionOrder": [],
    "standings": {
      "draws": 3,
      "goalsAgainst": 10,
      "goalsFor": 15,
      "losses": 0,
      "played": 0,
      "points": 15,
      "wins": 4,
    },
    "teamId": "team15",
    "teamName": "Test Team",
  },
]
`;
