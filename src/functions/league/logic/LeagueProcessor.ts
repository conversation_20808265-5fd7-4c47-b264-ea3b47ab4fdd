import { DBLeagueTable } from '@/model/leagueTable.js';
import { DBTeam } from '@/model/team.js';
import { logger } from '@/utils/logger.js';

export interface LeagueTeamCounts {
  [leagueId: string]: number;
}

export interface TeamMovement {
  teamId: string;
  fromLeagueId: string;
  toLeagueId: string;
}

export interface TeamStandings {
  teamName: string;
  points: number;
  goalDiff: number;
  goalsFor: number;
  wins: number;
}

export class LeagueProcessor {
  static groupTeamsByLeague(teams: DBTeam[]): Map<string, DBTeam[]> {
    return teams.reduce((acc, team) => {
      const leagueTeams = acc.get(team.leagueId) || [];
      leagueTeams.push(team);
      acc.set(team.leagueId, leagueTeams);
      return acc;
    }, new Map<string, DBTeam[]>());
  }

  static sortTeamsInLeague(teams: DBTeam[]): DBTeam[] {
    return [...teams].sort((a, b) => {
      if (a.standings.points !== b.standings.points) {
        return b.standings.points - a.standings.points;
      }

      const aGoalDiff = a.standings.goalsFor - a.standings.goalsAgainst;
      const bGoalDiff = b.standings.goalsFor - b.standings.goalsAgainst;
      if (aGoalDiff !== bGoalDiff) {
        return bGoalDiff - aGoalDiff;
      }

      if (a.standings.goalsFor !== b.standings.goalsFor) {
        return b.standings.goalsFor - a.standings.goalsFor;
      }

      if (a.standings.wins !== b.standings.wins) {
        return b.standings.wins - a.standings.wins;
      }

      return a.teamName.localeCompare(b.teamName);
    });
  }

  static validateTeamCounts(
    teamCounts: LeagueTeamCounts,
    leagueMap: Map<string, DBLeagueTable>
  ): boolean {
    return Object.entries(teamCounts).every(([leagueId, count]) => {
      const league = leagueMap.get(leagueId);
      if (!league) {
        logger.error(`League not found: ${leagueId}`);
        return false;
      }
      return count === league.rules.teamCount;
    });
  }

  static getTeamStandings(team: DBTeam): TeamStandings {
    return {
      teamName: team.teamName,
      points: team.standings.points,
      goalDiff: team.standings.goalsFor - team.standings.goalsAgainst,
      goalsFor: team.standings.goalsFor,
      wins: team.standings.wins,
    };
  }

  static processPromotionsAndRelegations(
    sortedLeagues: Map<string, DBTeam[]>,
    leagues: DBLeagueTable[]
  ): TeamMovement[] {
    const leagueMap = new Map(leagues.map((league) => [league.id, league]));
    const movements: TeamMovement[] = [];
    const teamCounts: LeagueTeamCounts = {};

    for (const [leagueId, teams] of sortedLeagues) {
      teamCounts[leagueId] = teams.length;
    }

    LeagueProcessor.processPromotions(sortedLeagues, leagueMap, teamCounts, movements);
    LeagueProcessor.processRelegations(sortedLeagues, leagueMap, teamCounts, movements);

    const isValid = LeagueProcessor.validateTeamCounts(teamCounts, leagueMap);
    if (!isValid) {
      logger.error('Invalid team distribution after promotions/relegations', { teamCounts });
      throw new Error('Invalid team distribution after promotions/relegations');
    }

    return movements;
  }

  private static processPromotions(
    sortedLeagues: Map<string, DBTeam[]>,
    leagueMap: Map<string, DBLeagueTable>,
    teamCounts: LeagueTeamCounts,
    movements: TeamMovement[]
  ): void {
    const sortedLeaguesByTier = [...leagueMap.values()].sort((a, b) => b.tier - a.tier);

    for (const league of sortedLeaguesByTier) {
      const teams = sortedLeagues.get(league.id);
      if (!teams || !league.parentLeagueId) continue;

      const parentLeague = leagueMap.get(league.parentLeagueId);
      if (!parentLeague) continue;

      const teamsToPromote = teams.slice(0, league.rules.promotion);
      for (const team of teamsToPromote) {
        movements.push({
          teamId: team.teamId,
          fromLeagueId: league.id,
          toLeagueId: parentLeague.id,
        });
        teamCounts[league.id]!--;
        teamCounts[parentLeague.id] = (teamCounts[parentLeague.id] || 0) + 1;
      }
    }
  }

  private static processRelegations(
    sortedLeagues: Map<string, DBTeam[]>,
    leagueMap: Map<string, DBLeagueTable>,
    teamCounts: LeagueTeamCounts,
    movements: TeamMovement[]
  ): void {
    const sortedLeaguesTopDown = [...leagueMap.values()].sort((a, b) => a.tier - b.tier);

    for (const league of sortedLeaguesTopDown) {
      const teams = sortedLeagues.get(league.id);
      if (!teams || league.childLeagueIds.length === 0) continue;

      const totalRelegation = league.rules.relegation;
      const teamsPerChild = totalRelegation / league.childLeagueIds.length;

      if (!Number.isInteger(teamsPerChild)) {
        logger.error('Invalid relegation configuration', {
          leagueId: league.id,
          totalRelegation,
          childLeagues: league.childLeagueIds.length,
        });
        continue;
      }

      const teamsToRelegate = teams.slice(-totalRelegation);
      LeagueProcessor.distributeRelegatedTeams(
        teamsToRelegate,
        league,
        teamsPerChild,
        teamCounts,
        movements
      );
    }
  }

  private static distributeRelegatedTeams(
    teamsToRelegate: DBTeam[],
    league: DBLeagueTable,
    teamsPerChild: number,
    teamCounts: LeagueTeamCounts,
    movements: TeamMovement[]
  ): void {
    for (let i = 0; i < teamsToRelegate.length; i++) {
      const childLeagueIndex = Math.floor(i / teamsPerChild);
      const childLeagueId = league.childLeagueIds[childLeagueIndex];

      if (!childLeagueId) {
        logger.error('Invalid child league index', {
          leagueId: league.id,
          childIndex: childLeagueIndex,
        });
        continue;
      }

      movements.push({
        teamId: teamsToRelegate[i]!.teamId,
        fromLeagueId: league.id,
        toLeagueId: childLeagueId,
      });
      teamCounts[league.id]!--;
      teamCounts[childLeagueId] = (teamCounts[childLeagueId] || 0) + 1;
    }
  }
}
