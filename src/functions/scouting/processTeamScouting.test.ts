import { DBPlayer } from '@/model/player.js';
import { ScoutedPlayersRecord } from '@/model/scouted-player.js';
import { ScoutingRequest } from '@/model/scouting.js';
import DynamoDbService from '@/services/database/dynamo/dynamo-db-service.js';
import { QueryResult } from '@/services/database/shared/database-service.js';
import createSqsEvent from '@/testing/createSqsEvent.js';
import { createPlayer } from '@/testing/playersTestUtils.js';
import { handler } from './processTeamScouting.js';

jest.mock('@/services/database/dynamo/dynamo-db-service');
jest.mock('@/utils/logger');

describe('Process Team Scouting Lambda', () => {
  const mockContext = {} as any;
  let getSpy: jest.SpyInstance;
  let querySpy: jest.SpyInstance;
  let insertSpy: jest.SpyInstance;

  const mockGameworldId = 'test-gameworld-id';
  const mockTeamId = 'test-team-id';

  const mockScoutingRequest: ScoutingRequest = {
    type: 'team',
    id: mockTeamId, // id is the teamId in this case
    teamId: mockTeamId,
    gameworldId: mockGameworldId,
    processAfter: Date.now(),
    pk: `${mockGameworldId}#${mockTeamId}`,
    requestId: 'request-1',
  };

  // Create mock players
  const mockPlayers: DBPlayer[] = Array.from({ length: 10 }, (_, i) =>
    createPlayer({
      gameworldId: mockGameworldId,
      playerId: `player-${i + 1}`,
      teamId: mockTeamId,
    })
  );

  // Mock query result
  const mockQueryResult: QueryResult<DBPlayer> = {
    items: mockPlayers,
    count: mockPlayers.length,
    scannedCount: mockPlayers.length,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    process.env.PLAYERS_TABLE_NAME = 'players-table';
    process.env.SCOUTED_PLAYERS_TABLE_NAME = 'scouted-players-table';

    getSpy = jest.spyOn(DynamoDbService.prototype, 'get');
    querySpy = jest.spyOn(DynamoDbService.prototype, 'query');
    insertSpy = jest.spyOn(DynamoDbService.prototype, 'insert');

    // Default mock for query to return all players
    querySpy.mockResolvedValue(mockQueryResult);
  });

  it('should successfully process a team scouting request with no existing scouted players', async () => {
    // Mock no existing scouted players record
    getSpy.mockResolvedValue(null);

    // Create SQS event with a scouting request
    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]) as any;

    // Execute the handler
    await handler(event, mockContext);

    // Verify players were queried
    expect(querySpy).toHaveBeenCalledWith(
      'players-table',
      {
        hashKey: {
          name: 'teamId',
          value: mockTeamId,
        },
        rangeKey: {
          name: 'gameworldId',
          value: mockGameworldId,
        },
      },
      'teamIndex',
      expect.objectContaining({
        projection: ['playerId'],
      })
    );

    // Verify scouted players record was checked
    expect(getSpy).toHaveBeenCalledWith('scouted-players-table', {
      gameworldId: mockGameworldId,
      teamId: mockTeamId,
    });

    // Verify scouted players record was updated with 5 players
    expect(insertSpy).toHaveBeenCalledWith(
      'scouted-players-table',
      expect.objectContaining({
        gameworldId: mockGameworldId,
        teamId: mockTeamId,
        scoutedPlayers: expect.arrayContaining([
          expect.objectContaining({
            playerId: expect.any(String),
            timestamp: expect.any(Number),
          }),
        ]),
      })
    );

    // Verify only 5 players were scouted
    const insertCall = insertSpy.mock.calls[0][1];
    expect(insertCall.scoutedPlayers.length).toBe(5);
  });

  it('should only scout players that have not been scouted before', async () => {
    // Mock existing scouted players record with some already scouted players
    const existingScoutedPlayers = [
      { playerId: 'player-1', timestamp: Date.now() - 86400000 }, // 1 day ago
      { playerId: 'player-2', timestamp: Date.now() - 86400000 },
      { playerId: 'player-3', timestamp: Date.now() - 86400000 },
    ];

    getSpy.mockResolvedValue({
      gameworldId: mockGameworldId,
      teamId: mockTeamId,
      scoutedPlayers: existingScoutedPlayers,
    } as ScoutedPlayersRecord);

    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]) as any;

    // Execute the handler
    await handler(event, mockContext);

    // Verify scouted players record was updated
    expect(insertSpy).toHaveBeenCalledWith(
      'scouted-players-table',
      expect.objectContaining({
        gameworldId: mockGameworldId,
        teamId: mockTeamId,
        scoutedPlayers: expect.arrayContaining([
          // Should include existing scouted players
          ...existingScoutedPlayers,
          // And new scouted players
          expect.objectContaining({
            playerId: expect.any(String),
            timestamp: expect.any(Number),
          }),
        ]),
      })
    );

    // Verify only new players were added (should be 5 new + 3 existing = 8)
    const insertCall = insertSpy.mock.calls[0][1];
    expect(insertCall.scoutedPlayers.length).toBe(8);

    // Verify the new players are different from the existing ones
    const newPlayerIds = insertCall.scoutedPlayers
      .filter((p) => !existingScoutedPlayers.some((ep) => ep.playerId === p.playerId))
      .map((p) => p.playerId);

    expect(newPlayerIds.length).toBe(5);
    expect(newPlayerIds).not.toContain('player-1');
    expect(newPlayerIds).not.toContain('player-2');
    expect(newPlayerIds).not.toContain('player-3');
  });

  it('should not update scouted players if all players have already been scouted', async () => {
    // Mock existing scouted players record with all players already scouted
    const existingScoutedPlayers = mockPlayers.map((p) => ({
      playerId: p.playerId,
      timestamp: Date.now() - 86400000, // 1 day ago
    }));

    getSpy.mockResolvedValue({
      gameworldId: mockGameworldId,
      teamId: mockTeamId,
      scoutedPlayers: existingScoutedPlayers,
    } as ScoutedPlayersRecord);

    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]) as any;

    // Execute the handler
    await handler(event, mockContext);

    // Verify scouted players record was not updated
    expect(insertSpy).not.toHaveBeenCalled();
  });

  it('should handle multiple scouting requests in batch', async () => {
    // Mock no existing scouted players record
    getSpy.mockResolvedValue(null);

    // Create multiple mock requests for the same team
    const secondRequest = {
      ...mockScoutingRequest,
      requestId: 'request-2',
    };

    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
      {
        body: secondRequest,
      },
    ]) as any;

    // Execute the handler
    await handler(event, mockContext);

    // Verify players were queried only once for the team
    expect(querySpy).toHaveBeenCalledTimes(1);

    // Verify scouted players record was updated twice (once for each request)
    expect(insertSpy).toHaveBeenCalledTimes(2);
  });

  it('should throw error if required environment variables are not set', async () => {
    delete process.env.PLAYERS_TABLE_NAME;
    delete process.env.SCOUTED_PLAYERS_TABLE_NAME;

    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]) as any;

    await expect(handler(event, mockContext)).rejects.toThrow(
      'PLAYERS_TABLE_NAME environment variable is not set'
    );
  });

  it('should handle database errors gracefully', async () => {
    // Mock query to throw error
    querySpy.mockRejectedValue(new Error('Database error'));

    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]) as any;

    // Verify error is thrown to trigger SQS retry
    await expect(handler(event, mockContext)).rejects.toThrow('Database error');

    // Verify no scouted players record was updated
    expect(insertSpy).not.toHaveBeenCalled();
  });

  it('should skip processing if no players are found for the team', async () => {
    // Mock empty query result
    querySpy.mockResolvedValue({
      items: [],
      count: 0,
      scannedCount: 0,
    });

    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]) as any;

    // Execute the handler
    await handler(event, mockContext);

    // Verify no scouted players record was updated
    expect(insertSpy).not.toHaveBeenCalled();
  });
});
