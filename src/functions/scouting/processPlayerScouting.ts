import { sqsMiddify } from '@/middleware/sqs/index.js';
import { SQSEvent } from '@/middleware/sqs/types.js';
import { ScoutingRequest } from '@/model/scouting.js';
import { logger } from '@/utils/logger.js';

async function main(event: SQSEvent<ScoutingRequest>) {
  // Get the scoutingRepository from the repositories
  const { scoutingRepository } = event.context.repositories;

  // Group requests by gameworld and team to minimize database operations
  const requestsByGameworldAndTeam: { [key: string]: ScoutingRequest[] } = {};

  // Collect all requests
  for (const record of event.Records) {
    const request = record.body;
    const key = `${request.gameworldId}#${request.teamId}`;
    if (!requestsByGameworldAndTeam[key]) {
      requestsByGameworldAndTeam[key] = [];
    }
    requestsByGameworldAndTeam[key].push(request);
    logger.debug('Collected player scouting request', { request });
  }

  try {
    // Process each team's requests
    for (const [key, requests] of Object.entries(requestsByGameworldAndTeam)) {
      const [gameworldId, teamId] = key.split('#');

      logger.debug('Processing batch of player scouting requests', {
        gameworldId,
        teamId,
        requestCount: requests.length,
      });

      // Extract player IDs directly from the requests
      const playerIds = requests.map((request) => request.id);

      // Save the players as scouted
      await scoutingRepository.saveScoutedPlayers(gameworldId!, teamId!, playerIds);

      logger.debug('Completed batch of player scouting requests', {
        gameworldId,
        teamId,
        processedCount: playerIds.length,
        playerIds,
      });
    }
  } catch (error) {
    logger.error('Failed to process player scouting requests', {
      error,
      requestCount: event.Records.length,
    });
    throw error; // Let SQS retry
  }
}

export const handler = sqsMiddify<ScoutingRequest>(main, {});
