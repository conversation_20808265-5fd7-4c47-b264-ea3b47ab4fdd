import { ScoutedPlayersRecord } from '@/model/scouted-player.js';
import { ScoutingRequest } from '@/model/scouting.js';
import DynamoDbService from '@/services/database/dynamo/dynamo-db-service.js';
import createSqsEvent from '@/testing/createSqsEvent.js';
import { handler } from './processPlayerScouting.js';

jest.mock('@/services/database/dynamo/dynamo-db-service');
jest.mock('@/utils/logger');

describe('Process Player Scouting Lambda', () => {
  const mockContext = {} as any;
  let getSpy: jest.SpyInstance;
  let insertSpy: jest.SpyInstance;

  const mockGameworldId = 'test-gameworld-id';
  const mockTeamId = 'test-team-id';
  const mockPlayerId = 'test-player-id';

  const mockScoutingRequest: ScoutingRequest = {
    type: 'player',
    id: mockPlayerId,
    teamId: mockTeamId,
    gameworldId: mockGameworldId,
    processAfter: Date.now(),
    pk: `${mockGameworldId}#${mockTeamId}`,
    requestId: 'request-1',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    process.env.SCOUTED_PLAYERS_TABLE_NAME = 'scouted-players-table';

    getSpy = jest.spyOn(DynamoDbService.prototype, 'get');
    insertSpy = jest.spyOn(DynamoDbService.prototype, 'insert');
  });

  it('should successfully process a player scouting request', async () => {
    // Mock existing scouted players record retrieval
    getSpy.mockResolvedValueOnce(null);

    // Create SQS event with a scouting request
    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]) as any;

    // Execute the handler
    await handler(event, mockContext);

    // Verify scouted players record was updated
    expect(insertSpy).toHaveBeenCalledWith(
      'scouted-players-table',
      expect.objectContaining({
        gameworldId: mockGameworldId,
        teamId: mockTeamId,
        scoutedPlayers: expect.arrayContaining([
          expect.objectContaining({
            playerId: mockPlayerId,
            timestamp: expect.any(Number),
          }),
        ]),
      })
    );
  });

  it('should update existing scouted players record when one exists', async () => {
    // Mock existing scouted players record retrieval
    getSpy.mockResolvedValueOnce({
      gameworldId: mockGameworldId,
      teamId: mockTeamId,
      scoutedPlayers: [
        {
          playerId: 'existing-player-1',
          timestamp: Date.now() - 86400000, // 1 day ago
        },
      ],
    } as ScoutedPlayersRecord);

    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]) as any;

    // Set a fixed date for testing
    const mockDate = new Date('2023-01-01T00:00:00Z');
    jest.spyOn(global.Date, 'now').mockReturnValue(mockDate.getTime());

    // Execute the handler
    await handler(event, mockContext);

    // Verify the insert includes both the existing and new player
    expect(insertSpy).toHaveBeenCalledWith(
      'scouted-players-table',
      expect.objectContaining({
        gameworldId: mockGameworldId,
        teamId: mockTeamId,
        scoutedPlayers: expect.arrayContaining([
          expect.objectContaining({
            playerId: 'existing-player-1',
            timestamp: expect.any(Number),
          }),
          expect.objectContaining({
            playerId: mockPlayerId,
            timestamp: mockDate.getTime(),
          }),
        ]),
      })
    );
  });

  it('should handle multiple scouting requests from different teams', async () => {
    // Create a request from a different team
    const secondTeamId = 'team-2';
    const secondRequest = {
      ...mockScoutingRequest,
      teamId: secondTeamId,
      requestId: 'request-2',
    };

    // Mock scouted players record lookups (no existing records)
    getSpy.mockResolvedValueOnce(null).mockResolvedValueOnce(null);

    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
      {
        body: secondRequest,
      },
    ]) as any;

    // Execute the handler
    await handler(event, mockContext);

    // Verify scouted players records were updated for both teams
    expect(insertSpy).toHaveBeenCalledTimes(2);

    // First team's record
    expect(insertSpy).toHaveBeenCalledWith(
      'scouted-players-table',
      expect.objectContaining({
        gameworldId: mockGameworldId,
        teamId: mockTeamId,
        scoutedPlayers: expect.arrayContaining([
          expect.objectContaining({
            playerId: mockPlayerId,
          }),
        ]),
      })
    );

    // Second team's record
    expect(insertSpy).toHaveBeenCalledWith(
      'scouted-players-table',
      expect.objectContaining({
        gameworldId: mockGameworldId,
        teamId: secondTeamId,
        scoutedPlayers: expect.arrayContaining([
          expect.objectContaining({
            playerId: mockPlayerId,
          }),
        ]),
      })
    );
  });

  it('should handle multiple players in a single batch', async () => {
    // Create a request for a different player
    const secondPlayerId = 'player-2';
    const secondRequest = {
      ...mockScoutingRequest,
      id: secondPlayerId,
      requestId: 'request-2',
    };

    // Mock scouted players record lookup (no existing record)
    getSpy.mockResolvedValueOnce(null);

    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
      {
        body: secondRequest,
      },
    ]) as any;

    // Execute the handler
    await handler(event, mockContext);

    // Verify scouted players record was updated once with both players
    expect(insertSpy).toHaveBeenCalledTimes(1);
    expect(insertSpy).toHaveBeenCalledWith(
      'scouted-players-table',
      expect.objectContaining({
        gameworldId: mockGameworldId,
        teamId: mockTeamId,
        scoutedPlayers: expect.arrayContaining([
          expect.objectContaining({
            playerId: mockPlayerId,
          }),
          expect.objectContaining({
            playerId: secondPlayerId,
          }),
        ]),
      })
    );
  });

  it('should throw error if required environment variables are not set', async () => {
    delete process.env.SCOUTED_PLAYERS_TABLE_NAME;

    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]) as any;

    await expect(handler(event, mockContext)).rejects.toThrow(
      'SCOUTED_PLAYERS_TABLE_NAME environment variable is not set'
    );
  });

  it('should handle database errors gracefully', async () => {
    // Mock get to throw error
    getSpy.mockRejectedValueOnce(new Error('Database error'));

    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]) as any;

    // Verify error is thrown to trigger SQS retry
    await expect(handler(event, mockContext)).rejects.toThrow('Database error');

    // Verify no scouted players record was updated
    expect(insertSpy).not.toHaveBeenCalled();
  });
});
