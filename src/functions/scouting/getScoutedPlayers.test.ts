/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */

import { getManagerFromDatabase } from '@/functions/manager/logic.js';
import { DBManager } from '@/model/manager.js';
import { DBPlayer } from '@/model/player.js';
import { ScoutedPlayersRecord } from '@/model/scouted-player.js';
import DynamoDbService from '@/services/database/dynamo/dynamo-db-service.js';
import createHttpEvent from '@/testing/createHttpEvent.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';
import { handler } from './getScoutedPlayers.js';

// Mock dependencies
jest.mock('@/services/database/dynamo/dynamo-db-service');
jest.mock('@/utils/getUser');
jest.mock('@/functions/manager/logic');
jest.mock('@/utils/logger', () => ({
  logger: {
    debug: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    info: jest.fn(),
    addContext: jest.fn(),
    logEventIfEnabled: jest.fn(),
  },
}));

describe('getScoutedPlayers', () => {
  // Mock data
  const mockUserId = 'test-user-id';
  const mockGameworldId = 'test-gameworld-id';
  const mockTeamId = 'test-team-id';

  // Mock manager with access to the team
  const mockManager: DBManager = {
    managerId: mockUserId,
    createdAt: Date.now(),
    lastActive: Date.now(),
    teamId: mockTeamId,
    gameworldId: mockGameworldId,
    scoutTokens: 5,
    superScoutTokens: 1,
  };

  // Mock scouted players record
  const mockScoutedPlayersRecord: ScoutedPlayersRecord = {
    gameworldId: mockGameworldId,
    teamId: mockTeamId,
    scoutedPlayers: [
      { playerId: 'player1', timestamp: Date.now() - 1000 },
      { playerId: 'player2', timestamp: Date.now() - 2000 },
      { playerId: 'player3', timestamp: Date.now() - 3000 },
      { playerId: 'player4', timestamp: Date.now() - 4000 },
      { playerId: 'player5', timestamp: Date.now() - 5000 },
    ],
  };

  // Mock player details
  const createMockPlayer = (playerId: string): DBPlayer => ({
    gameworldId: mockGameworldId,
    teamId: 'original-team-id',
    leagueId: 'test-league-id',
    playerId,
    firstName: `First${playerId}`,
    surname: `Last${playerId}`,
    attributes: {
      reflexes: { current: 50, potential: 70 },
      positioning: { current: 50, potential: 70 },
      shotStopping: { current: 50, potential: 70 },
      tackling: { current: 50, potential: 70 },
      marking: { current: 50, potential: 70 },
      heading: { current: 50, potential: 70 },
      finishing: { current: 50, potential: 70 },
      pace: { current: 50, potential: 70 },
      crossing: { current: 50, potential: 70 },
      passing: { current: 50, potential: 70 },
      vision: { current: 50, potential: 70 },
      ballControl: { current: 50, potential: 70 },
    },
    age: 25,
    value: 1000000,
    seed: 12345,
  });

  const mockPlayers = mockScoutedPlayersRecord.scoutedPlayers.map((sp) =>
    createMockPlayer(sp.playerId)
  );

  beforeEach(() => {
    jest.resetAllMocks();

    // Set up environment variables
    process.env.SCOUTED_PLAYERS_TABLE_NAME = 'scouted-players-table';
    process.env.PLAYERS_TABLE_NAME = 'players-table';
    process.env.GET_MANAGER_LAMBDA_ARN = 'get-manager-lambda-arn';

    // Mock getUser
    (getUser as jest.Mock).mockReturnValue(mockUserId);

    // Mock getManagerFromDatabase
    (getManagerFromDatabase as jest.Mock).mockResolvedValue(mockManager);

    // Mock DynamoDbService.get
    (DynamoDbService.prototype.get as jest.Mock).mockResolvedValue(mockScoutedPlayersRecord);

    // Mock DynamoDbService.batchGet
    (DynamoDbService.prototype.batchGet as jest.Mock).mockResolvedValue(mockPlayers);
  });

  it('should return 400 if path parameters are missing', async () => {
    // Create event with missing path parameters
    const event = createHttpEvent({
      pathParameters: {},
    });

    const result = await handler(event, {} as any);

    expect(result).toEqual(
      buildResponse(400, JSON.stringify({ error: 'Missing required path parameters' }))
    );
  });

  it('should return 401 if user is not authenticated', async () => {
    // Mock getUser to return null (unauthenticated)
    (getUser as jest.Mock).mockReturnValueOnce(null);

    const event = createHttpEvent({
      pathParameters: {
        gameworldId: mockGameworldId,
        teamId: mockTeamId,
      },
    });

    const result = await handler(event, {} as any);

    expect(result).toEqual(buildResponse(401, JSON.stringify({ error: 'Unauthorized' })));
  });

  it('should return 500 if manager is not found', async () => {
    // Mock getManagerFromDatabase to throw an error (manager not found)
    (getManagerFromDatabase as jest.Mock).mockRejectedValueOnce(new Error('Manager not found'));

    const event = createHttpEvent({
      pathParameters: {
        gameworldId: mockGameworldId,
        teamId: mockTeamId,
      },
    });

    const result = await handler(event, {} as any);

    expect(result).toEqual(buildResponse(500, JSON.stringify({ error: 'Internal server error' })));
  });

  it('should return 403 if manager does not have access to the team', async () => {
    // Mock getManagerFromDatabase to return a manager without access to the team
    const managerWithoutAccess = { ...mockManager, teamId: 'different-team-id' };
    (getManagerFromDatabase as jest.Mock).mockResolvedValueOnce(managerWithoutAccess);

    const event = createHttpEvent({
      pathParameters: {
        gameworldId: mockGameworldId,
        teamId: mockTeamId,
      },
    });

    const result = await handler(event, {} as any);

    expect(result).toEqual(
      buildResponse(403, JSON.stringify({ error: 'Forbidden - No access to this team' }))
    );
  });

  it('should return 500 if there is an error getting the manager from database', async () => {
    // Mock getManagerFromDatabase to throw an error
    (getManagerFromDatabase as jest.Mock).mockRejectedValueOnce(new Error('Database error'));

    const event = createHttpEvent({
      pathParameters: {
        gameworldId: mockGameworldId,
        teamId: mockTeamId,
      },
    });

    const result = await handler(event, {} as any);

    expect(result).toEqual(buildResponse(500, JSON.stringify({ error: 'Internal server error' })));
  });

  it('should return 400 if nextToken is invalid', async () => {
    const event = createHttpEvent({
      pathParameters: {
        gameworldId: mockGameworldId,
        teamId: mockTeamId,
      },
      queryStringParameters: {
        nextToken: 'invalid-token',
      },
    });

    const result = await handler(event, {} as any);

    expect(result).toEqual(
      buildResponse(400, JSON.stringify({ error: 'Invalid pagination token' }))
    );
  });

  it('should return empty array if no scouted players are found', async () => {
    // Mock DynamoDbService.get to return null (no scouted players)
    (DynamoDbService.prototype.get as jest.Mock).mockResolvedValueOnce(null);

    const event = createHttpEvent({
      pathParameters: {
        gameworldId: mockGameworldId,
        teamId: mockTeamId,
      },
    });

    const result = await handler(event, {} as any);

    expect(result).toEqual(
      buildResponse(
        200,
        JSON.stringify({
          scoutedPlayers: [],
          pagination: { hasMore: false },
        })
      )
    );
  });

  it('should return scouted players with details and pagination', async () => {
    const event = createHttpEvent({
      pathParameters: {
        gameworldId: mockGameworldId,
        teamId: mockTeamId,
      },
      queryStringParameters: {
        limit: '2',
      },
    });

    const result = await handler(event, {} as any);
    const parsedResult = JSON.parse(result.body);

    // Verify the structure of the response
    expect(parsedResult).toHaveProperty('scoutedPlayers');
    expect(parsedResult).toHaveProperty('pagination');
    expect(parsedResult.pagination).toHaveProperty('hasMore', true);
    expect(parsedResult.pagination).toHaveProperty('nextToken');

    // Verify the number of returned players matches the limit
    expect(parsedResult.scoutedPlayers.length).toBe(2);

    // Verify each player has the lastScoutedAt property
    parsedResult.scoutedPlayers.forEach((player: any) => {
      expect(player).toHaveProperty('lastScoutedAt');
    });

    // Verify players are sorted by timestamp (newest first)
    expect(parsedResult.scoutedPlayers[0].lastScoutedAt).toBeGreaterThan(
      parsedResult.scoutedPlayers[1].lastScoutedAt
    );

    // Verify DynamoDbService.batchGet was called with the correct parameters
    expect(DynamoDbService.prototype.batchGet).toHaveBeenCalledWith(
      process.env.PLAYERS_TABLE_NAME,
      expect.arrayContaining([
        expect.objectContaining({
          gameworldId: mockGameworldId,
          playerId: expect.any(String),
        }),
      ])
    );
  });

  it('should handle pagination with nextToken', async () => {
    // Create a valid nextToken (base64 encoded JSON)
    const validNextToken = Buffer.from(JSON.stringify({ index: 2 })).toString('base64');

    const event = createHttpEvent({
      pathParameters: {
        gameworldId: mockGameworldId,
        teamId: mockTeamId,
      },
      queryStringParameters: {
        limit: '2',
        nextToken: validNextToken,
      },
    });

    const result = await handler(event, {} as any);
    const parsedResult = JSON.parse(result.body);

    // Verify pagination is working correctly
    expect(parsedResult.scoutedPlayers.length).toBe(2);

    // The returned players should be different from the first page
    const playerIds = parsedResult.scoutedPlayers.map((p: any) => p.playerId);
    expect(playerIds).not.toContain(mockScoutedPlayersRecord.scoutedPlayers[0]!.playerId);
    expect(playerIds).not.toContain(mockScoutedPlayersRecord.scoutedPlayers[1]!.playerId);
  });

  it('should handle the last page of pagination correctly', async () => {
    // Create a nextToken that points to the last items
    const validNextToken = Buffer.from(JSON.stringify({ index: 3 })).toString('base64');

    const event = createHttpEvent({
      pathParameters: {
        gameworldId: mockGameworldId,
        teamId: mockTeamId,
      },
      queryStringParameters: {
        limit: '3',
        nextToken: validNextToken,
      },
    });

    const result = await handler(event, {} as any);
    const parsedResult = JSON.parse(result.body);

    // Verify we're on the last page
    expect(parsedResult.pagination.hasMore).toBe(false);
    expect(parsedResult.pagination.nextToken).toBeUndefined();

    // Should return the remaining players
    expect(parsedResult.scoutedPlayers.length).toBe(2); // Only 2 players left
  });

  it('should return 500 if there is an unexpected error', async () => {
    // Mock DynamoDbService.get to throw an error
    (DynamoDbService.prototype.get as jest.Mock).mockRejectedValueOnce(new Error('Database error'));

    const event = createHttpEvent({
      pathParameters: {
        gameworldId: mockGameworldId,
        teamId: mockTeamId,
      },
    });

    const result = await handler(event, {} as any);

    expect(result).toEqual(buildResponse(500, JSON.stringify({ error: 'Internal server error' })));
  });
});
