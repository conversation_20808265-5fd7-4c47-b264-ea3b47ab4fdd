import { Player } from '@/entities/Player.js';
import { PlayerAttributes } from '@/entities/PlayerAttributes.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { extractCurrentAttributes } from '@/utils/attributeUtils.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';
import { logger } from '@/utils/logger.js';

interface PathParameters {
  gameworldId: string;
  teamId: string;
}

interface QueryStringParameters {
  limit?: string;
  nextToken?: string;
}

export type GetScoutedPlayersEvent = HttpEvent<void, PathParameters, QueryStringParameters>;

// Interface for the return type of scouted players
type ScoutedPlayerResponse = Omit<
  Player,
  'attributes' | 'seed' | 'gameworldId' | 'overallStats' | 'matchHistory'
> & {
  attributes: PlayerAttributes;
  lastScoutedAt: number;
};

const main = async function (event: GetScoutedPlayersEvent) {
  const { playerRepository, managerRepository } = event.context.repositories;
  try {
    const { gameworldId, teamId } = event.pathParameters || {};

    if (!gameworldId || !teamId) {
      return buildResponse(400, JSON.stringify({ error: 'Missing required path parameters' }));
    }

    // Get the user ID from the request
    const userId = getUser(event);
    if (!userId) {
      return buildResponse(401, JSON.stringify({ error: 'Unauthorized' }));
    }

    // Invoke the getManager Lambda to get the manager information
    try {
      const manager = await managerRepository.getManagerById(userId);
      if (!manager) {
        return buildResponse(404, JSON.stringify({ error: 'Manager not found' }));
      }

      // Check if the manager has access to the requested team
      const hasAccess = manager.team?.teamId === teamId && manager.gameworldId === gameworldId;

      if (!hasAccess) {
        logger.warn('Manager does not have access to the requested team', {
          managerId: userId,
          teamId,
          gameworldId,
        });
        return buildResponse(403, JSON.stringify({ error: 'Forbidden - No access to this team' }));
      }

      logger.debug('Manager has access to the requested team', {
        managerId: userId,
        teamId,
        gameworldId,
      });
    } catch (error) {
      logger.error('Error invoking getManager Lambda', { error });
      return buildResponse(500, JSON.stringify({ error: 'Internal server error' }));
    }

    // Get pagination parameters
    const limit = event.queryStringParameters?.limit
      ? parseInt(event.queryStringParameters.limit)
      : 20;
    // FIXME: const nextTokenParam = event.queryStringParameters?.nextToken;
    const nextToken: { index: number } | undefined = undefined;

    // Get the scouted players record
    const scoutedPlayersRecord = await playerRepository.getPlayersScoutedByTeam(
      gameworldId,
      teamId,
      limit,
      nextToken
    );

    if (!scoutedPlayersRecord || !scoutedPlayersRecord?.length) {
      return buildResponse(
        200,
        JSON.stringify({
          scoutedPlayers: [],
          pagination: { hasMore: false },
        })
      );
    }

    // Map player details to scouted players with the required format
    const scoutedPlayersWithDetails = scoutedPlayersRecord.map((player) => {
      logger.debug('Scouted player details', {
        player,
      });

      // Extract only the current attribute values using the helper function
      const currentAttributes = extractCurrentAttributes(player.player.attributes);
      return {
        teamId: player.player.team?.teamId || '',
        leagueId: player.player.leagueId,
        playerId: player.player.playerId,
        firstName: player.player.firstName,
        surname: player.player.surname,
        attributes: currentAttributes,
        age: player.player.age,
        value: player.player.value,
        lastScoutedAt: player.scoutedAt,
      };
    });

    logger.debug('Retrieved scouted players with details', {
      gameworldId,
      teamId,
      requestedCount: limit,
      returnedCount: scoutedPlayersWithDetails.length,
    });

    return buildResponse(
      200,
      JSON.stringify({
        scoutedPlayers: scoutedPlayersWithDetails,
        /* FIXME: pagination: {
          hasMore,
          nextToken: newNextToken,
        },*/
      })
    );
  } catch (error) {
    logger.error('Error retrieving scouted players', { error });
    return buildResponse(500, JSON.stringify({ error: 'Internal server error' }));
  }
};

export const handler = httpMiddify(main, {});
