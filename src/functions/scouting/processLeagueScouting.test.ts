import { DBPlayer } from '@/model/player.js';
import { ScoutedPlayersRecord } from '@/model/scouted-player.js';
import { ScoutingRequest } from '@/model/scouting.js';
import DynamoDbService from '@/services/database/dynamo/dynamo-db-service.js';
import { QueryResult } from '@/services/database/shared/database-service.js';
import createSqsEvent from '@/testing/createSqsEvent.js';
import { createPlayer } from '@/testing/playersTestUtils.js';
import { seededRandom } from '@/utils/seeded-random.js';
import { handler } from './processLeagueScouting.js';

// Mock dependencies
jest.mock('@/services/database/dynamo/dynamo-db-service');
jest.mock('@/utils/seeded-random');
jest.mock('@/utils/logger', () => ({
  logger: {
    debug: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    addContext: jest.fn(),
    logEventIfEnabled: jest.fn(),
  },
}));

describe('Process League Scouting', () => {
  const mockContext = {} as any;
  let querySpy: jest.SpyInstance;
  let getSpy: jest.SpyInstance;
  let insertSpy: jest.SpyInstance;

  // Mock data
  const mockGameworldId = 'test-gameworld-id';
  const mockLeagueId = 'test-league-id';
  const mockTeamId = 'test-team-id';

  // Create mock players
  const createMockPlayer = (id: number): DBPlayer =>
    createPlayer({
      playerId: `player-${id}`,
      teamId: `team-${id}`,
      leagueId: mockLeagueId,
      firstName: `First${id}`,
      surname: `Last${id}`,
    });

  // Create mock players array
  const mockPlayers: DBPlayer[] = Array.from({ length: 20 }, (_, i) => createMockPlayer(i + 1));

  // Create mock scouting request
  const mockScoutingRequest: ScoutingRequest = {
    type: 'league',
    id: mockLeagueId,
    requestId: 'test-request-id',
    pk: `${mockGameworldId}#${mockTeamId}`,
    teamId: mockTeamId,
    gameworldId: mockGameworldId,
    processAfter: Date.now() - 1000, // Ready to process
  };

  beforeEach(() => {
    // Set environment variables
    process.env.PLAYERS_TABLE_NAME = 'players-table';
    process.env.SCOUTED_PLAYERS_TABLE_NAME = 'scouted-players-table';

    jest.resetAllMocks();

    // Setup spies
    querySpy = jest.spyOn(DynamoDbService.prototype, 'query');
    getSpy = jest.spyOn(DynamoDbService.prototype, 'get');
    insertSpy = jest.spyOn(DynamoDbService.prototype, 'insert');
    jest.mocked(seededRandom).mockReturnValue(Math.random());

    // Mock query to return players
    querySpy.mockResolvedValue({
      items: mockPlayers,
      lastEvaluatedKey: undefined,
    } as QueryResult<DBPlayer>);

    // Reset all mocks
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should process a league scouting request and update scouted players', async () => {
    // Create SQS event with a scouting request
    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]);

    // Mock get to return no existing record
    getSpy.mockResolvedValue(null);

    // Set a fixed date for testing
    const mockDate = new Date('2023-01-01T00:00:00Z');
    jest.spyOn(global.Date, 'now').mockReturnValue(mockDate.getTime());

    // Execute the handler
    await handler(event, mockContext);

    // Verify query was called with correct parameters
    expect(querySpy).toHaveBeenCalledWith(
      'players-table',
      {
        hashKey: {
          name: 'leagueId',
          value: mockLeagueId,
        },
        rangeKey: {
          name: 'gameworldId',
          value: mockGameworldId,
        },
      },
      'leagueIndex',
      expect.objectContaining({ exclusiveStartKey: undefined, projection: ['playerId'] })
    );

    // Verify get was called to check for existing record
    expect(getSpy).toHaveBeenCalledWith('scouted-players-table', {
      gameworldId: mockGameworldId,
      teamId: mockTeamId,
    });

    // Verify insert was called with correct parameters
    expect(insertSpy).toHaveBeenCalledWith(
      'scouted-players-table',
      expect.objectContaining({
        gameworldId: mockGameworldId,
        teamId: mockTeamId,
        scoutedPlayers: expect.arrayContaining([
          expect.objectContaining({
            playerId: expect.any(String),
            timestamp: mockDate.getTime(),
          }),
        ]),
      })
    );

    // Verify 5 players were selected
    const insertedRecord = insertSpy.mock.calls[0][1] as ScoutedPlayersRecord;
    expect(insertedRecord.scoutedPlayers).toHaveLength(5);
  });

  it('should update existing scouted players record when one exists', async () => {
    // Create SQS event with a scouting request
    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]);

    // Create existing scouted players record
    const existingRecord: ScoutedPlayersRecord = {
      gameworldId: mockGameworldId,
      teamId: mockTeamId,
      scoutedPlayers: [
        {
          playerId: 'existing-player-1',
          timestamp: Date.now() - 86400000, // 1 day ago
        },
        {
          playerId: 'player-1', // This one will be updated
          timestamp: Date.now() - 86400000, // 1 day ago
        },
      ],
    };

    // Mock get to return existing record
    getSpy.mockResolvedValue(existingRecord);

    // Set a fixed date for testing
    const mockDate = new Date('2023-01-01T00:00:00Z');
    jest.spyOn(global.Date, 'now').mockReturnValue(mockDate.getTime());

    // Execute the handler
    await handler(event, mockContext);

    // Verify insert was called with correct parameters
    expect(insertSpy).toHaveBeenCalledWith(
      'scouted-players-table',
      expect.objectContaining({
        gameworldId: mockGameworldId,
        teamId: mockTeamId,
        scoutedPlayers: expect.arrayContaining([
          expect.objectContaining({
            playerId: 'existing-player-1',
            timestamp: expect.any(Number),
          }),
        ]),
      })
    );

    // Verify the record contains both existing and new players
    const insertedRecord = insertSpy.mock.calls[0][1] as ScoutedPlayersRecord;
    expect(insertedRecord.scoutedPlayers.length).toBeGreaterThanOrEqual(6); // 2 existing + at least 4 new

    // Check that the existing player's timestamp was updated
    const updatedPlayer = insertedRecord.scoutedPlayers.find((p) => p.playerId === 'player-1');
    expect(updatedPlayer).toBeDefined();
    expect(updatedPlayer?.timestamp).toBe(mockDate.getTime());
  });

  it('should handle multiple scouting requests for the same league', async () => {
    // Create SQS event with multiple scouting requests for the same league
    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
      {
        body: {
          ...mockScoutingRequest,
          teamId: 'another-team-id',
        },
      },
    ]);

    // Mock get to return no existing records
    getSpy.mockResolvedValue(null);

    // Execute the handler
    await handler(event, mockContext);

    // Verify query was called only once for the league
    expect(querySpy).toHaveBeenCalledTimes(1);

    // Verify get and insert were called twice (once for each team)
    expect(getSpy).toHaveBeenCalledTimes(2);
    expect(insertSpy).toHaveBeenCalledTimes(2);
  });

  it('should handle errors when updating scouted players', async () => {
    // Create SQS event with a scouting request
    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]);

    // Mock get to return no existing record
    getSpy.mockResolvedValue(null);

    // Mock insert to throw an error
    const mockError = new Error('Database error');
    insertSpy.mockRejectedValue(mockError);

    // Execute the handler and expect it to throw
    await expect(handler(event, mockContext)).rejects.toThrow(mockError);
  });

  it('should throw an error if SCOUTED_PLAYERS_TABLE_NAME is not set', async () => {
    // Create SQS event with a scouting request
    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]);

    // Unset the environment variable
    delete process.env.SCOUTED_PLAYERS_TABLE_NAME;

    // Execute the handler and expect it to throw
    await expect(handler(event, mockContext)).rejects.toThrow(
      'SCOUTED_PLAYERS_TABLE_NAME environment variable is not set'
    );
  });

  it('should handle pagination when retrieving players from DynamoDB', async () => {
    // Create SQS event with a scouting request
    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]) as any;

    // Mock query to return players with pagination
    const firstBatch = mockPlayers.slice(0, 10);
    const secondBatch = mockPlayers.slice(10);
    const lastEvaluatedKey = { gameworldId: mockGameworldId, playerId: 'player-10' };

    querySpy
      .mockResolvedValueOnce({
        items: firstBatch,
        lastEvaluatedKey,
      } as QueryResult<DBPlayer>)
      .mockResolvedValueOnce({
        items: secondBatch,
        lastEvaluatedKey: undefined,
      } as QueryResult<DBPlayer>);

    // Mock get to return no existing record
    getSpy.mockResolvedValue(null);

    // Execute the handler
    await handler(event, mockContext);

    // Verify query was called twice (for pagination)
    expect(querySpy).toHaveBeenCalledTimes(2);
    expect(querySpy).toHaveBeenNthCalledWith(
      1,
      'players-table',
      {
        hashKey: {
          name: 'leagueId',
          value: mockLeagueId,
        },
        rangeKey: {
          name: 'gameworldId',
          value: mockGameworldId,
        },
      },
      'leagueIndex',
      expect.objectContaining({ exclusiveStartKey: undefined, projection: ['playerId'] })
    );
    expect(querySpy).toHaveBeenNthCalledWith(
      2,
      'players-table',
      {
        hashKey: {
          name: 'leagueId',
          value: mockLeagueId,
        },
        rangeKey: {
          name: 'gameworldId',
          value: mockGameworldId,
        },
      },
      'leagueIndex',
      expect.objectContaining({
        exclusiveStartKey: { gameworldId: 'test-gameworld-id', playerId: 'player-10' },
        projection: ['playerId'],
      })
    );

    // Verify insert was called with a record containing 5 players
    const insertedRecord = insertSpy.mock.calls[0][1] as ScoutedPlayersRecord;
    expect(insertedRecord.scoutedPlayers).toHaveLength(5);
  });
});
