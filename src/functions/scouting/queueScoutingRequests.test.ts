import { ScoutingRequest } from '@/model/scouting.js';
import DynamoDbService from '@/services/database/dynamo/dynamo-db-service.js';
import { SNS } from '@/services/sns/index.js';
import { ScheduledEvent } from 'aws-lambda';
import { handler } from './queueScoutingRequests.js';

jest.mock('@/services/database/dynamo/dynamo-db-service');
jest.mock('@/services/sns');
jest.mock('@/utils/logger');

describe('Queue Scouting Requests Lambda', () => {
  const mockContext = {} as any;
  let scanSpy: jest.SpyInstance;
  let deleteSpy: jest.SpyInstance;
  let publishBatchSpy: jest.SpyInstance;

  const mockRequests: ScoutingRequest[] = [
    {
      type: 'player',
      id: 'player-1',
      teamId: 'team-1',
      gameworldId: 'gameworld-1',
      processAfter: Date.now() - 1000,
      pk: 'gameworld-1#team-1',
      requestId: 'request-1',
    },
    {
      type: 'team',
      id: 'team-2',
      teamId: 'team-1',
      gameworldId: 'gameworld-1',
      processAfter: Date.now() - 500, // Ready to process
      pk: 'gameworld-1#team-1',
      requestId: 'request-2',
    },
  ];

  beforeEach(() => {
    process.env.SCOUTING_REQUESTS_TABLE_NAME = 'scouting-requests-table';
    process.env.SCOUTING_TOPIC_ARN = 'test-topic-arn';

    scanSpy = jest.spyOn(DynamoDbService.prototype, 'scan');
    deleteSpy = jest.spyOn(DynamoDbService.prototype, 'delete');
    publishBatchSpy = jest.spyOn(SNS.prototype, 'publishBatch');
  });

  afterEach(() => {
    jest.clearAllMocks();
    delete process.env.SCOUTING_REQUESTS_TABLE_NAME;
    delete process.env.SCOUTING_TOPIC_ARN;
  });

  it('should process ready requests and publish to SNS', async () => {
    scanSpy.mockResolvedValue({ items: mockRequests });
    publishBatchSpy.mockResolvedValue({});
    deleteSpy.mockResolvedValue({});

    const event = {} as ScheduledEvent;
    await handler(event, mockContext);

    // Verify scan was called with correct filter
    expect(scanSpy).toHaveBeenCalledWith('scouting-requests-table', {
      filterExpression: 'processAfter <= :now',
      expressionAttributeValues: expect.any(Object),
    });

    // Verify SNS publish was called with correct entries
    expect(publishBatchSpy).toHaveBeenCalledWith('test-topic-arn', [
      {
        Id: '0',
        Message: JSON.stringify(mockRequests[0]),
        MessageAttributes: {
          DataType: { DataType: 'String', StringValue: 'player' },
        },
      },
      {
        Id: '1',
        Message: JSON.stringify(mockRequests[1]),
        MessageAttributes: {
          DataType: { DataType: 'String', StringValue: 'team' },
        },
      },
    ]);

    // Verify requests were deleted
    expect(deleteSpy).toHaveBeenCalledTimes(2);
    expect(deleteSpy).toHaveBeenCalledWith('scouting-requests-table', {
      pk: 'gameworld-1#team-1',
      requestId: 'request-1',
    });
    expect(deleteSpy).toHaveBeenCalledWith('scouting-requests-table', {
      pk: 'gameworld-1#team-1',
      requestId: 'request-2',
    });
  });

  it('should handle empty request list', async () => {
    scanSpy.mockResolvedValue({ items: [] });

    const event = {} as ScheduledEvent;
    await handler(event, mockContext);

    expect(publishBatchSpy).not.toHaveBeenCalled();
    expect(deleteSpy).not.toHaveBeenCalled();
  });

  it('should process requests in batches of 10', async () => {
    // Create 15 mock requests
    const largeMockRequests = Array.from({ length: 15 }, (_, i) => ({
      type: 'player',
      id: `player-${i}`,
      teamId: 'team-1',
      gameworldId: 'gameworld-1',
      processAfter: Date.now() - 1000,
    }));

    scanSpy.mockResolvedValue({ items: largeMockRequests });
    publishBatchSpy.mockResolvedValue({});
    deleteSpy.mockResolvedValue({});

    const event = {} as ScheduledEvent;
    await handler(event, mockContext);

    // Should have called publishBatch twice (10 items + 5 items)
    expect(publishBatchSpy).toHaveBeenCalledTimes(2);

    // First batch should have 10 items
    expect(publishBatchSpy.mock.calls[0][1]).toHaveLength(10);
    // Second batch should have 5 items
    expect(publishBatchSpy.mock.calls[1][1]).toHaveLength(5);
  });

  it('should throw error if table name is not set', async () => {
    delete process.env.SCOUTING_REQUESTS_TABLE_NAME;

    const event = {} as ScheduledEvent;
    await expect(handler(event, mockContext)).rejects.toThrow(
      'SCOUTING_REQUESTS_TABLE_NAME environment variable not set'
    );
  });

  it('should throw error if topic ARN is not set', async () => {
    delete process.env.SCOUTING_TOPIC_ARN;

    const event = {} as ScheduledEvent;
    await expect(handler(event, mockContext)).rejects.toThrow(
      'SCOUTING_TOPIC_ARN environment variable not set'
    );
  });

  it('should handle SNS publish failures', async () => {
    scanSpy.mockResolvedValue({ items: mockRequests });
    publishBatchSpy.mockRejectedValue(new Error('SNS publish failed'));

    const event = {} as ScheduledEvent;
    await expect(handler(event, mockContext)).rejects.toThrow('SNS publish failed');

    // Verify no requests were deleted
    expect(deleteSpy).not.toHaveBeenCalled();
  });
});
