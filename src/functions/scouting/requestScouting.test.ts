import { DBManager } from '@/model/manager.js';
import { DBTeam } from '@/model/team.js';
import DynamoDbService from '@/services/database/dynamo/dynamo-db-service.js';
import createHttpEvent from '@/testing/createHttpEvent.js';
import { handler } from './requestScouting.js';

jest.mock('@/services/database/dynamo/dynamo-db-service');
jest.mock('@/utils/getUser', () => ({
  getUser: jest.fn().mockReturnValue('test-user-id'),
}));

describe('Request Scouting Lambda', () => {
  const mockContext = {} as any;
  let getSpy: jest.SpyInstance;
  let updateSpy: jest.SpyInstance;
  let insertSpy: jest.SpyInstance;

  const mockManager: DBManager = {
    managerId: 'test-user-id',
    teamId: 'test-team-id',
    gameworldId: 'test-gameworld-id',
    scoutTokens: 3,
  } as DBManager;

  const mockTeam: DBTeam = {
    teamId: 'test-team-id',
    gameworldId: 'test-gameworld-id',
    balance: 10000,
  } as DBTeam;

  beforeEach(() => {
    process.env.MANAGERS_TABLE_NAME = 'managers-table';
    process.env.TEAMS_TABLE_NAME = 'teams-table';
    process.env.SCOUTING_REQUESTS_TABLE_NAME = 'scouting-requests-table';

    getSpy = jest.spyOn(DynamoDbService.prototype, 'get');
    updateSpy = jest.spyOn(DynamoDbService.prototype, 'update');
    insertSpy = jest.spyOn(DynamoDbService.prototype, 'insert');

    // getSpy
    //   .mockImplementationOnce(() => Promise.resolve(mockManager))
    //   .mockImplementationOnce(() => Promise.resolve(mockTeam));
    updateSpy.mockImplementation(() => Promise.resolve());
    insertSpy.mockImplementation(() => Promise.resolve());
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should successfully process a valid scouting request', async () => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date(1740178572294));

    getSpy
      .mockImplementationOnce(() => Promise.resolve(mockManager))
      .mockImplementationOnce(() => Promise.resolve(mockTeam));
    const event = createHttpEvent({
      httpMethod: 'POST',
      body: {
        type: 'player',
        id: 'test-player-id',
      },
    });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(200);
    expect(body).toEqual({
      message: 'Scouting request successful',
      remainingTokens: 2,
      newBalance: 5000,
    });

    // Verify database operations
    expect(updateSpy).toHaveBeenCalledTimes(2);
    expect(updateSpy).toHaveBeenCalledWith(
      'managers-table',
      { managerId: 'test-user-id' },
      { scoutTokens: 2 }
    );
    expect(updateSpy).toHaveBeenCalledWith(
      'teams-table',
      { teamId: 'test-team-id', gameworldId: 'test-gameworld-id' },
      { balance: 5000 }
    );

    // Verify scouting request insertion
    expect(insertSpy).toHaveBeenCalledWith('scouting-requests-table', {
      type: 'player',
      id: 'test-player-id',
      teamId: 'test-team-id',
      gameworldId: 'test-gameworld-id',
      pk: 'test-gameworld-id#test-team-id',
      processAfter: 1740185772294,
      requestId: expect.any(String),
    });

    jest.useRealTimers();
  });

  it('should validate request body types', async () => {
    const event = createHttpEvent({
      httpMethod: 'POST',
      body: {
        type: 'invalid-type',
        id: 'test-id',
      },
    });

    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(422);
  });

  it('should reject when manager has no scout tokens', async () => {
    getSpy.mockReset();
    getSpy
      .mockImplementationOnce(() => Promise.resolve({ ...mockManager, scoutTokens: 0 }))
      .mockImplementationOnce(() => Promise.resolve(mockTeam));

    const event = createHttpEvent({
      httpMethod: 'POST',
      body: {
        type: 'player',
        id: 'test-player-id',
      },
    });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(400);
    expect(body.error).toBe('No scouting tokens remaining');
    expect(updateSpy).not.toHaveBeenCalled();
  });

  it('should reject when team has insufficient funds', async () => {
    getSpy
      .mockReset()
      .mockImplementationOnce(() => Promise.resolve(mockManager))
      .mockImplementationOnce(() => Promise.resolve({ ...mockTeam, balance: 4000 }));

    const event = createHttpEvent({
      httpMethod: 'POST',
      body: {
        type: 'player',
        id: 'test-player-id',
      },
    });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(400);
    expect(body.error).toContain('Insufficient funds');
    expect(updateSpy).not.toHaveBeenCalled();
  });

  it('should return 404 when manager is not found', async () => {
    getSpy.mockImplementationOnce(() => Promise.resolve(null));

    const event = createHttpEvent({
      httpMethod: 'POST',
      body: {
        type: 'player',
        id: 'test-player-id',
      },
    });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(404);
    expect(body.error).toBe('Manager not found');
    expect(updateSpy).not.toHaveBeenCalled();
  });

  it('should return 404 when team is not found', async () => {
    getSpy
      .mockImplementationOnce(() => Promise.resolve(mockManager))
      .mockImplementationOnce(() => Promise.resolve(null));

    const event = createHttpEvent({
      httpMethod: 'POST',
      body: {
        type: 'player',
        id: 'test-player-id',
      },
    });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(404);
    expect(body.error).toBe('Team not found');
    expect(updateSpy).not.toHaveBeenCalled();
  });

  it('should throw error when MANAGERS_TABLE_NAME is not set', async () => {
    delete process.env.MANAGERS_TABLE_NAME;

    const event = createHttpEvent({
      httpMethod: 'POST',
      body: {
        type: 'player',
        id: 'test-player-id',
      },
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(500);
    expect(response.body).toBe('MANAGERS_TABLE_NAME environment variable not set');
    expect(updateSpy).not.toHaveBeenCalled();
  });

  it('should throw error when TEAMS_TABLE_NAME is not set', async () => {
    delete process.env.TEAMS_TABLE_NAME;

    const event = createHttpEvent({
      httpMethod: 'POST',
      body: {
        type: 'player',
        id: 'test-player-id',
      },
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(500);
    expect(response.body).toBe('TEAMS_TABLE_NAME environment variable not set');
    expect(updateSpy).not.toHaveBeenCalled();
  });

  it('should throw error when SCOUTING_REQUESTS_TABLE_NAME is not set', async () => {
    delete process.env.SCOUTING_REQUESTS_TABLE_NAME;

    const event = createHttpEvent({
      httpMethod: 'POST',
      body: {
        type: 'player',
        id: 'test-player-id',
      },
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(500);
    expect(response.body).toBe('SCOUTING_REQUESTS_TABLE_NAME environment variable not set');
    expect(updateSpy).not.toHaveBeenCalled();
  });

  it.each(['player', 'team', 'league'])('should accept valid type: %s', async (type) => {
    getSpy
      .mockImplementationOnce(() => Promise.resolve(mockManager))
      .mockImplementationOnce(() => Promise.resolve(mockTeam));

    const event = createHttpEvent({
      httpMethod: 'POST',
      body: {
        type,
        id: 'test-id',
      },
    });

    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(200);
  });
});
