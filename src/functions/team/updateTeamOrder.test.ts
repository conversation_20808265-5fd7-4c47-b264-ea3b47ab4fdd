import { DBManager } from '@/model/manager.js';
import { DBTeam } from '@/model/team.js';
import { buildResponse } from '@/utils/buildResponse.js';

// Import the main function directly instead of the handler
import { main } from './updateTeamOrder.js';

// Mock the repositories
const mockTeamRepository = {
  getTeam: jest.fn(),
  updateTeamSelectionOrder: jest.fn().mockResolvedValue({}),
};

const mockManagerRepository = {
  getManager: jest.fn(),
};

// Mock the getUser function
jest.mock('@/utils/getUser.js', () => ({
  getUser: jest.fn().mockImplementation(() => 'user1'),
}));

describe('updateTeamOrder', () => {
  const mockManager: DBManager = {
    managerId: 'user1',
    teamId: 'team1',
    gameworldId: 'gameworld1',
    createdAt: Date.now(),
    lastActive: Date.now(),
    scoutTokens: 3,
    superScoutTokens: 0,
  };

  const mockTeam: DBTeam = {
    teamId: 'team1',
    gameworldId: 'gameworld1',
    selectionOrder: ['player1', 'player2', 'player3'],
    teamName: 'Test Team',
    tier: 1,
    balance: 100000,
    played: 0,
    wins: 0,
    draws: 0,
    losses: 0,
    goalsFor: 0,
    goalsAgainst: 0,
    points: 0,
    leagueId: 'league1',
  };

  // Create a mock event with repositories in context
  const createMockEvent = (body: any, pathParameters: any) => ({
    body,
    pathParameters,
    headers: {
      'Content-Type': 'application/json',
    },
    requestContext: {
      authorizer: {
        userId: 'user1',
      },
    },
    context: {
      repositories: {
        teamRepository: mockTeamRepository,
        managerRepository: mockManagerRepository,
      },
    },
  });

  beforeEach(() => {
    jest.clearAllMocks();
    mockTeamRepository.getTeam.mockReset();
    mockTeamRepository.updateTeamSelectionOrder.mockReset();
    mockManagerRepository.getManager.mockReset();
  });

  it('should return 400 if body is missing', async () => {
    const event = createMockEvent(
      {},
      {
        teamId: 'team1',
        gameworldId: 'gameworld1',
      }
    );

    const response = await main(event);
    expect(response).toEqual(
      buildResponse(400, JSON.stringify({ error: 'Body must be an array of player IDs' }))
    );
  });

  it('should return 400 if playerIds is not an array', async () => {
    const event = createMockEvent(
      { playerIds: 'not-an-array' },
      {
        teamId: 'team1',
        gameworldId: 'gameworld1',
      }
    );

    const response = await main(event);
    expect(response).toEqual(
      buildResponse(
        400,
        JSON.stringify({ error: 'Body must be an array of player IDs', playerIds: 'not-an-array' })
      )
    );
  });

  it('should return 403 if manager is not found', async () => {
    // Mock the getManager method to return null
    mockManagerRepository.getManager.mockResolvedValue(null);

    const event = createMockEvent(
      { playerIds: ['player1', 'player2', 'player3'] },
      {
        teamId: 'team1',
        gameworldId: 'gameworld1',
      }
    );

    const response = await main(event);

    expect(mockManagerRepository.getManager).toHaveBeenCalledWith('user1');
    expect(response).toEqual(
      buildResponse(403, JSON.stringify({ error: 'Not authorized to update this team' }))
    );
  });

  it('should return 403 if manager does not own the team', async () => {
    const wrongManager = { ...mockManager, teamId: 'different-team' };
    mockManagerRepository.getManager.mockResolvedValue(wrongManager);

    const event = createMockEvent(
      { playerIds: ['player1', 'player2', 'player3'] },
      {
        teamId: 'team1',
        gameworldId: 'gameworld1',
      }
    );

    const response = await main(event);
    expect(response).toEqual(
      buildResponse(403, JSON.stringify({ error: 'Not authorized to update this team' }))
    );
  });

  it('should return 404 if team is not found', async () => {
    mockManagerRepository.getManager.mockResolvedValue(mockManager);
    mockTeamRepository.getTeam.mockResolvedValue(null);

    const event = createMockEvent(
      { playerIds: ['player1', 'player2', 'player3'] },
      {
        teamId: 'team1',
        gameworldId: 'gameworld1',
      }
    );

    const response = await main(event);

    expect(mockTeamRepository.getTeam).toHaveBeenCalledWith('gameworld1', 'team1', false);
    expect(response).toEqual(buildResponse(404, JSON.stringify({ error: 'Team not found' })));
  });

  it('should update team order successfully', async () => {
    mockManagerRepository.getManager.mockResolvedValue(mockManager);
    mockTeamRepository.getTeam.mockResolvedValue(mockTeam);
    mockTeamRepository.updateTeamSelectionOrder.mockResolvedValue({});

    const newOrder = ['player3', 'player1', 'player2'];
    const event = createMockEvent(
      { playerIds: newOrder },
      {
        teamId: 'team1',
        gameworldId: 'gameworld1',
      }
    );

    const response = await main(event);

    expect(mockManagerRepository.getManager).toHaveBeenCalledWith('user1');
    expect(mockTeamRepository.getTeam).toHaveBeenCalledWith('gameworld1', 'team1', false);
    expect(mockTeamRepository.updateTeamSelectionOrder).toHaveBeenCalledWith(
      'team1',
      'gameworld1',
      newOrder
    );
    expect(response).toEqual(
      buildResponse(200, JSON.stringify({ message: 'Team order updated successfully' }))
    );
  });
});
