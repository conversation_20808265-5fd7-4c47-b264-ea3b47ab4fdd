import { buildResponse } from '@/utils/buildResponse.js';

// Import the main function directly
import { Player } from '@/entities/Player.js';
import { Team } from '@/entities/Team.js';
import createHttpEvent from '@/testing/createHttpEvent.js';
import { handler } from './getTeam.js';
import { TeamsFactory } from "@/testing/factories/teamFactory.js";
import { PlayerFactory } from '@/testing/factories/playerFactory.js';

// Mock the repositories
const mockTeamRepository = {
  getTeam: jest.fn(),
};

// Mock the getUser function
jest.mock('@/utils/getUser.js', () => ({
  getUser: jest.fn().mockReturnValue('defaultUserId'),
}));

// Mock the httpMiddify function to return the main function directly
jest.mock('@/middleware/rest/index.js', () => ({
  httpMiddify: jest.fn((fn) => fn),
}));

describe('getTeam', () => {
  const mockContext = {} as any; // Mock context object
  const mockTeam: Team = TeamsFactory.build();

  const mockPlayers: Player[] = [
    PlayerFactory.build({
      playerId: 'player1',
      team: { teamId: 'team1' },
      gameworldId: 'gameworld1',
      firstName: 'John',
      surname: 'Doe',
      age: 25,
      value: 1000000,
      seed: 123,
      attributes: {} as any,
    }),
    PlayerFactory.build({
      playerId: 'player2',
      team: 'team1',
      gameworldId: 'gameworld1',
      firstName: 'Jane',
      surname: 'Smith',
      age: 23,
      value: 1200000,
      seed: 456,
      attributes: {} as any,
    }),
    PlayerFactory.build({
      playerId: 'player3',
      team: 'team1',
      gameworldId: 'gameworld1',
      firstName: 'Bob',
      surname: 'Wilson',
      age: 28,
      value: 800000,
      seed: 789,
      attributes: {} as any,
    }),
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    mockTeamRepository.getTeam.mockReset();
  });

  it('should return 404 if team is not found', async () => {
    mockTeamRepository.getTeam.mockResolvedValue(null);

    const event = createHttpEvent({
      pathParameters: {
        teamId: 'team1',
        gameworldId: 'gameworld1',
      },
    });

    const response = await handler(event, mockContext);

    expect(mockTeamRepository.getTeam).toHaveBeenCalledWith('gameworld1', 'nonexistent', false);
    expect(response).toEqual(buildResponse(404, JSON.stringify({ error: 'Team not found' })));
  });

  it('should return team without players when includePlayers is false', async () => {
    mockTeamRepository.getTeam.mockResolvedValue(mockTeam);

    const event = createHttpEvent({
      pathParameters: {
        teamId: 'team1',
        gameworldId: 'gameworld1',
      },
    });

    const response = await handler(event, mockContext);

    expect(mockTeamRepository.getTeam).toHaveBeenCalledWith('gameworld1', 'team1', false);
    expect(response).toEqual(buildResponse(200, JSON.stringify({ ...mockTeam, players: [] })));
  });

  it('should return team with ordered players when includePlayers is true', async () => {
    // Create a team with players already included (as the repository would return)
    const teamWithPlayers = {
      ...mockTeam,
      players: mockPlayers,
    };

    mockTeamRepository.getTeam.mockResolvedValue(teamWithPlayers);

    const event = createHttpEvent({
      pathParameters: {
        teamId: 'team1',
        gameworldId: 'gameworld1',
      },
      queryStringParameters: {
        includePlayers: 'true',
      },
    });

    const response = await handler(event, mockContext);
    const responseBody = JSON.parse(response.body);

    expect(mockTeamRepository.getTeam).toHaveBeenCalledWith('gameworld1', 'team1', true);

    // Verify players are ordered according to selectionOrder
    expect(responseBody.players[0].playerId).toBe('player1');
    expect(responseBody.players[1].playerId).toBe('player2');
    expect(responseBody.players[2].playerId).toBe('player3');
  });
});
