import { <PERSON>tity, PrimaryKey, Property, Unique } from '@mikro-orm/core';

/**
 * Entities describing the end date for a season
 */
@Entity({ tableName: 'inbox' })
@Unique({ properties: ['id'] })
export class Inbox {
  @PrimaryKey({ type: 'uuid', defaultRaw: 'uuid_generate_v4()' })
  id!: string;

  @Property({ type: 'bigint' })
  date!: number; // time in millis when the message was sent

  @Property()
  message!: string; // message to be sent to the user

  @Property()
  extra!: string; // extra information to be sent to the user
}
