import { Entity, OneToOne, Property, type Rel } from '@mikro-orm/core';
import { League } from './League.js';

@Entity({ tableName: 'league_rules' })
export class LeagueRules {
  constructor(rules?: LeagueRules) {
    if (!rules) return;
    this.promotionSpots = rules.promotionSpots;
    this.relegationSpots = rules.relegationSpots;
    this.teamCount = rules.teamCount;
    this.league = rules.league;
  }

  @OneToOne({
    entity: () => League,
    inversedBy: 'leagueRules',
    owner: true,
    primary: true,
    fieldName: 'league_id',
  })
  league!: Rel<League>;

  @Property()
  promotionSpots!: number;

  @Property()
  relegationSpots!: number;

  @Property()
  teamCount!: number;

  @Property()
  maximumPrize: number = 0;
  @Property()
  minimumPrize: number = 0;
}
