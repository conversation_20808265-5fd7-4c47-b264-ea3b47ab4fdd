import { Embeddable, Property } from '@mikro-orm/core';
import { Scorer } from './types.js';

// Custom transformer for number array properties
const numberArrayTransformer = {
  from: (value: string[]): [number, number] => {
    if (!value) return [0, 0];
    return [Number(value[0]), Number(value[1])];
  },
  to: (value: [number, number]): [number, number] => value,
};

@Embeddable()
export class MatchStats {
  @Property({ type: 'int[]', transformer: numberArrayTransformer })
  possession!: [number, number];

  @Property({ type: 'int[]', transformer: numberArrayTransformer })
  shots!: [number, number];

  @Property({ type: 'int[]', transformer: numberArrayTransformer })
  shotsOnTarget!: [number, number];

  @Property({ type: 'int[]', transformer: numberArrayTransformer })
  corners!: [number, number];

  @Property({ type: 'int[]', transformer: numberArrayTransformer })
  fouls!: [number, number];

  @Property({ type: 'int[]', transformer: numberArrayTransformer })
  yellowCards!: [number, number];

  @Property({ type: 'int[]', transformer: numberArrayTransformer })
  redCards!: [number, number];

  @Property({ type: 'int[]', transformer: numberArrayTransformer })
  passes!: [number, number];

  @Property({ type: 'int[]', transformer: numberArrayTransformer })
  passAccuracy!: [number, number];

  @Property({ type: 'int[]', transformer: numberArrayTransformer })
  tackles!: [number, number];

  @Property({ type: 'int[]', transformer: numberArrayTransformer })
  interceptions!: [number, number];

  @Property({ type: 'int[]', transformer: numberArrayTransformer })
  score!: [number, number];

  @Property({ type: 'json', nullable: true })
  scorers?: Scorer[];
}
