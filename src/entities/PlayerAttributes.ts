import { Entity, OneToOne, PrimaryKeyProp, Property, type Rel } from '@mikro-orm/core';
import { Player } from './Player.js';

@Entity({ tableName: 'player_attributes' })
export class PlayerAttributes {
  [PrimaryKeyProp]?: 'player';

  @OneToOne({ entity: () => Player, fieldName: 'player_id', primary: true })
  player!: Rel<Player>;

  @Property()
  reflexesCurrent!: number;

  @Property()
  reflexesPotential!: number;

  @Property()
  positioningCurrent!: number;

  @Property()
  positioningPotential!: number;

  @Property()
  shotStoppingCurrent!: number;

  @Property()
  shotStoppingPotential!: number;

  @Property()
  tacklingCurrent!: number;

  @Property()
  tacklingPotential!: number;

  @Property()
  markingCurrent!: number;

  @Property()
  markingPotential!: number;

  @Property()
  headingCurrent!: number;

  @Property()
  headingPotential!: number;

  @Property()
  finishingCurrent!: number;

  @Property()
  finishingPotential!: number;

  @Property()
  paceCurrent!: number;

  @Property()
  pacePotential!: number;

  @Property()
  crossingCurrent!: number;

  @Property()
  crossingPotential!: number;

  @Property()
  passingCurrent!: number;

  @Property()
  passingPotential!: number;

  @Property()
  visionCurrent!: number;

  @Property()
  visionPotential!: number;

  @Property()
  ballControlCurrent!: number;

  @Property()
  ballControlPotential!: number;

  @Property({ type: 'float', precision: 2 })
  stamina!: number;
}
