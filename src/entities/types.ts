export interface Scorer {
  playerId: string;
  playerName: string;
  team: number;
  goalTime: {
    minute: number;
    half: number;
  }[];
}

export interface MatchEvent {
  localisationId: string;
  substitutions: {
    team?: string;
    homeTeam?: string;
    oppTeam?: string;
    awayTeam?: string;
    player?: string;
    oppPlayer?: string;
    nextPlayer?: string;
    homeScore?: string;
    awayScore?: string;
  };
  minute: number;
  half: number;
}