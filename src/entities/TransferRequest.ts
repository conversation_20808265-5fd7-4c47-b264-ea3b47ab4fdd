import { Entity, ManyToOne, Opt, PrimaryKey, Property, type Rel, Unique } from '@mikro-orm/core';
import { Player } from './Player.js';
import { Team } from './Team.js';

/**
 * Requests to buy a player from another team
 */
@Entity({ tableName: 'transfer_request' })
@Unique({ properties: ['player', 'buyer'] })
export class TransferRequest {
  @PrimaryKey({ type: 'uuid', defaultRaw: 'uuid_generate_v4()' })
  id!: string;

  @Property({ type: 'bigint' })
  date!: number; // time in millis request was sent

  @ManyToOne({ entity: () => Player, fieldName: 'player_id' })
  player!: Rel<Player>; // player to be transferred

  @ManyToOne({ entity: () => Team, fieldName: 'buyerTeam' })
  buyer!: Rel<Team>; // buying team

  @ManyToOne({ entity: () => Team, fieldName: 'sellerTeam' })
  seller!: Rel<Team>; // selling team

  @Property({ type: 'bigint' })
  value!: number; // price buyer is willing to pay

  @Property({ type: 'bigint' })
  counterOfferTime: number & Opt = 0; // time in millis when the counter offer was sent

  @Property({ type: 'bigint' })
  counterOfferValue: number & Opt = 0; // value of the counter offer
}
