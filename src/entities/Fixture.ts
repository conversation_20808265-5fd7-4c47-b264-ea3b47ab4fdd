import {
  Embedded,
  <PERSON>tity,
  ManyToOne,
  PrimaryKey,
  PrimaryKeyProp,
  Property,
  Ref,
  Unique,
} from '@mikro-orm/core';
import { League } from './League.js';
import { MatchStats } from './MatchStats.js';
import { Team } from './Team.js';
import { MatchEvent } from './types.js';

@Entity({ tableName: 'fixture' })
@Unique({ properties: ['gameworldId', 'league', 'fixtureId'] })
export class Fixture {
  [PrimaryKeyProp]?: 'fixtureId';

  @PrimaryKey({ type: 'uuid' })
  fixtureId!: string;

  @Property({ type: 'uuid' })
  gameworldId!: string;

  @ManyToOne(() => League)
  league!: Ref<League>;

  @ManyToOne(() => Team)
  homeTeam!: Ref<Team>;

  @ManyToOne(() => Team)
  awayTeam!: Ref<Team>;

  @Property({ type: 'bigint' })
  date!: number;

  @Embedded(() => MatchStats, { nullable: true })
  stats?: MatchStats;

  @Property({ type: 'json', nullable: true })
  events?: MatchEvent[];

  @Property()
  played: boolean = false;

  @Property({ type: 'bigint', nullable: true })
  simulatedAt?: number;

  @Property({ type: 'bigint', nullable: true })
  seed?: number;
}
