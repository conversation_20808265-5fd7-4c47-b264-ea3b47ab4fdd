# Email Service

This service provides functionality to send emails to multiple recipients using AWS SES (Simple Email Service) with MJML-based templates.

## Features

- Send emails to multiple recipients
- Create beautiful responsive email templates using MJML
- Customizable logo and unsubscribe link
- Error handling and logging

## Prerequisites

Before using this service, ensure you have:

1. AWS SES set up and configured
2. Verified email addresses or domains in SES
3. Appropriate IAM permissions for sending emails via SES

## Installation

The service requires the following dependencies:

```bash
npm install @aws-sdk/client-ses mjml
```

These dependencies should already be included in the project's package.json.

## Usage

### Basic Usage

```typescript
import { sendTemplatedEmail } from '@/services/email/index.js';

// Send an email to multiple recipients
await sendTemplatedEmail(
  ['<EMAIL>', '<EMAIL>'],
  'Email Subject',
  '<p>This is the email content</p>'
);
```

### Advanced Usage

```typescript
import { sendTemplatedEmail } from '@/services/email/index.js';

// Send an email with custom logo and unsubscribe link
await sendTemplatedEmail(
  ['<EMAIL>', '<EMAIL>'],
  'Welcome to Our Platform',
  '<h1>Welcome!</h1><p>Thank you for joining us.</p>',
  'https://example.com/logo.png',
  'https://example.com/unsubscribe?email={email}',
  '<EMAIL>'
);
```

### Using Individual Functions

The service exports several functions that can be used independently:

```typescript
import { 
  createEmailTemplate, 
  convertMjmlToHtml, 
  sendEmail 
} from '@/services/email/index.js';

// Create an MJML template
const mjmlTemplate = createEmailTemplate(
  '<p>Email content</p>',
  'https://example.com/logo.png',
  'https://example.com/unsubscribe'
);

// Convert MJML to HTML
const htmlContent = convertMjmlToHtml(mjmlTemplate);

// Send the email
await sendEmail(
  ['<EMAIL>'],
  'Email Subject',
  htmlContent,
  '<EMAIL>'
);
```

## API Reference

### sendTemplatedEmail

```typescript
sendTemplatedEmail(
  recipients: string[],
  subject: string,
  content: string,
  logoUrl?: string,
  unsubscribeUrl?: string,
  source?: string
): Promise<void>
```

Sends an email with an MJML template to multiple recipients.

- `recipients`: Array of email addresses to send to
- `subject`: Email subject
- `content`: Main content of the email (can include HTML)
- `logoUrl`: (Optional) URL of the logo to display
- `unsubscribeUrl`: (Optional) URL for the unsubscribe link
- `source`: (Optional) Sender email address

### createEmailTemplate

```typescript
createEmailTemplate(
  content: string,
  logoUrl?: string,
  unsubscribeUrl?: string
): string
```

Creates an MJML email template with the specified content, logo, and unsubscribe link.

### convertMjmlToHtml

```typescript
convertMjmlToHtml(mjmlTemplate: string): string
```

Converts an MJML template string to HTML.

### sendEmail

```typescript
sendEmail(
  recipients: string[],
  subject: string,
  content: string,
  source?: string
): Promise<void>
```

Sends an HTML email to multiple recipients using AWS SES.

## Example

See `src/examples/email-example.ts` for a complete example of how to use this service.