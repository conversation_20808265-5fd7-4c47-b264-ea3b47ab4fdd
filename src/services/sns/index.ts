import { logger } from '@/utils/logger.js';
import type { Tracer } from '@aws-lambda-powertools/tracer';
import {
  PublishBatchCommand,
  PublishBatchCommandInput,
  PublishCommand,
  PublishInput,
  SNSClient,
  SNSClientConfig,
} from '@aws-sdk/client-sns';
import Ajv, { Options as AjvOptions, type ValidateFunction } from 'ajv';

export interface SNSPublishEvent {
  subject?: string;
  data: unknown;
}

export interface SNSBatchPublishEntry {
  Id: string;
  Message: string;
  Subject?: string;
  MessageAttributes?: Record<string, { DataType: string; StringValue: string }>;
}

export interface SNSServiceOptions {
  configuration?: SNSClientConfig;
  tracer?: Tracer;
  /**
   * Schema validation can only be used if publish is called with a JSON object.
   *
   * @type {object}
   * @memberof SNSServiceOptions
   */
  schema?: object;
  /**
   * Options for the AJV validator.
   *
   * @type {AjvOptions}
   * @memberof SNSServiceOptions
   */
  validatorOptions?: AjvOptions;
}

export class SNS {
  private readonly sns: SNSClient;

  private readonly validateFn?: ValidateFunction;

  constructor(protected readonly _options: SNSServiceOptions) {
    const snsOptions = {
      ..._options.configuration,
    };

    this.sns = new SNSClient(snsOptions);

    if (_options.tracer) {
      this.sns = _options.tracer.captureAWSv3Client(this.sns);
    }

    if (_options.schema) {
      this.validateFn = new Ajv({
        ..._options.validatorOptions,
        useDefaults: true,
      }).compile(_options.schema);
    }
  }

  private validate(entity: unknown): void {
    if (!this.validateFn) {
      return;
    }

    // if the entity is an object
    if (typeof entity !== 'object') {
      logger.warn('Cannot validate. Entity is not an object', { entity });
      return;
    }

    const valid = this.validateFn(entity);
    if (!valid) {
      logger.error('Entity does not match schema', JSON.stringify(this.validateFn.errors, null, 2));
      throw new Error('Entity does not match schema', {
        cause: this.validateFn.errors,
      });
    }
  }

  async publish(
    topic: string,
    event: SNSPublishEvent,
    additionalOpts: Partial<PublishInput> = {}
  ): Promise<void> {
    this.validate(event.data);

    const input: PublishInput = {
      TopicArn: topic,
      Subject: event.subject,
      Message: typeof event.data !== 'string' ? JSON.stringify(event.data) : event.data,
      ...additionalOpts,
    };

    logger.debug('Publishing event to SNS', { input });
    const command = new PublishCommand(input);
    try {
      const result = await this.sns.send(command);
      logger.debug('SNS publish result', { result });
    } catch (err) {
      logger.error('SNS publish error', err as Error);
      throw err;
    }
  }

  /**
   * Publishes multiple messages to an SNS topic in a single batch operation.
   * Maximum of 10 messages per batch as per AWS limits.
   *
   * @param topic - The ARN of the SNS topic
   * @param entries - Array of messages to publish (max 10)
   * @throws Error if more than 10 entries are provided
   */
  async publishBatch(topic: string, entries: SNSBatchPublishEntry[]): Promise<void> {
    if (entries.length > 10) {
      throw new Error('Cannot publish more than 10 messages in a single batch');
    }

    // Validate each entry's data if schema is provided
    if (this._options.schema) {
      entries.forEach((entry) => {
        try {
          const data: unknown = JSON.parse(entry.Message);
          this.validate(data);
        } catch (err) {
          logger.error('Failed to validate message', {
            entry,
            error: err,
            validationErrors: this.validateFn?.errors || 'No validation errors available',
            parseError: err instanceof SyntaxError ? 'Invalid JSON format' : undefined,
          });
          throw err;
        }
      });
    }

    const input: PublishBatchCommandInput = {
      TopicArn: topic,
      PublishBatchRequestEntries: entries,
    };

    logger.debug('Publishing batch to SNS', { input });
    const command = new PublishBatchCommand(input);

    try {
      const result = await this.sns.send(command);

      if (result.Failed && result.Failed.length > 0) {
        logger.error('Some messages failed to publish', { failed: result.Failed });
        throw new Error('Some messages failed to publish', {
          cause: result.Failed,
        });
      }

      logger.debug('SNS publish batch result', { result });
    } catch (err) {
      logger.error('SNS publish batch error', err as Error);
      throw err;
    }
  }
}
