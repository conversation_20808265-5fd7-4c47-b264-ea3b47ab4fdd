import {
  Manager,
  NotificationCategory,
  NotificationChannel,
  NotificationPreferences,
} from '@/entities/Manager.js';
import { Team } from '@/entities/Team.js';
import { sqs } from '@/functions/transfers/transferUtils.js';
import { SQS } from '@/services/sqs/sqs.js';
import { ManagerRepository } from '@/storage-interface/managers/manager-repository.js';
import { Auction, DBTransferRequest } from '@/storage-interface/transfers/index.js';
import { EmailEvent } from '@/types/generated/email-event.js';
import { logger } from '@/utils/logger.js';
import { tracer } from '@/utils/tracer.js';
import { Expo, ExpoPushMessage } from 'expo-server-sdk';

type Notification = {
  subject: string;
  content: string;
  title: string;
  category: NotificationCategory;
};

export class NotificationManager {
  private notificationPreferences: NotificationPreferences | undefined;
  private email: string | undefined;
  private pushToken: string | undefined;
  private sqs = new SQS({ tracer });
  private static instance: NotificationManager;

  private constructor() {}

  static getInstance() {
    if (!NotificationManager.instance) {
      NotificationManager.instance = new NotificationManager();
    }
    return NotificationManager.instance;
  }

  public async loadManagerPreferences(managerId: string, managerRepository: ManagerRepository) {
    const manager = await managerRepository.getManagerById(managerId);
    if (!manager) {
      throw new Error(`Manager with ID ${managerId} not found`);
    }
    this.notificationPreferences = manager.notificationPreferences;
    this.email = manager.email;
    this.pushToken = manager.pushToken;
  }

  public assignManagerPreferences(manager: Manager) {
    this.notificationPreferences = manager.notificationPreferences;
    this.email = manager.email;
    this.pushToken = manager.pushToken;
  }

  private shouldSendNotification(
    prefs: NotificationPreferences,
    category: NotificationCategory,
    channel: NotificationChannel
  ): boolean {
    return Boolean(prefs?.[category]?.[channel]);
  }

  private async sendEmailNotification(notification: Notification) {
    try {
      const emailEvent: EmailEvent = {
        recipients: [this.email!],
        subject: notification.subject,
        content: notification.content,
        title: notification.title,
      };

      await sqs.send(process.env.EMAIL_QUEUE_URL!, JSON.stringify(emailEvent));
    } catch (error) {
      logger.error('Failed to send email to queue', {
        error,
        notification,
      });
    }
  }

  private async sendPushNotification(notification: Notification) {
    try {
      // Check if we have a valid push token
      if (!this.pushToken) {
        logger.warn('No push token available for notification', { notification });
        return;
      }

      // Validate the push token
      if (!Expo.isExpoPushToken(this.pushToken)) {
        logger.error('Invalid Expo push token', { pushToken: this.pushToken });
        return;
      }

      // Create a new Expo SDK client
      const expo = new Expo();

      // Construct the message
      const message: ExpoPushMessage = {
        to: this.pushToken,
        sound: 'default',
        title: notification.title,
        body: notification.content,
        data: {
          category: notification.category,
          content: notification.content,
        },
        // Set priority to high to ensure timely delivery
        priority: 'high',
      };

      try {
        // Send the push notification
        const ticketChunk = await expo.sendPushNotificationsAsync([message]);
        const ticket = ticketChunk[0];

        if (!ticket) {
          logger.error('Failed to send push notification', {
            error: 'No ticket returned',
            notification,
          });
          return;
        }
        if (ticket.status === 'error') {
          logger.error('Error sending push notification', {
            error: ticket.message,
            details: ticket.details,
            notification,
          });

          // Handle specific error cases
          if (ticket.details && ticket.details.error === 'DeviceNotRegistered') {
            logger.warn('Device is no longer registered for push notifications', {
              pushToken: this.pushToken,
            });
            // In a real implementation, you might want to update the database to clear this token
          }
        } else {
          logger.info('Push notification sent successfully', {
            ticketId: ticket.id,
            category: notification.category,
          });
        }
      } catch (error) {
        logger.error('Failed to send push notification', {
          error,
          notification,
        });
      }
    } catch (error) {
      logger.error('Error in sendPushNotification', {
        error,
        notification,
      });
    }
  }

  private sendNotification(notification: Notification) {
    // Don't send notifications if we don't have preferences
    if (!this.notificationPreferences) {
      return;
    }

    const promises = [];

    if (
      this.email &&
      this.shouldSendNotification(
        this.notificationPreferences,
        notification.category,
        NotificationChannel.EMAIL
      )
    ) {
      promises.push(this.sendEmailNotification(notification));
    }
    if (
      this.pushToken &&
      this.shouldSendNotification(
        this.notificationPreferences,
        notification.category,
        NotificationChannel.PUSH
      )
    ) {
      promises.push(this.sendPushNotification(notification));
    }

    return Promise.all(promises);
  }

  public auctionFailed(auction: Auction) {
    const playerName = auction.player.$.firstName + ' ' + auction.player.$.surname;
    const notification: Notification = {
      subject: `Auction ended without bids for ${playerName}`,
      content: `<p>Your player ${playerName} was listed for transfer but received no bids after 3 attempts.</p>
              <p>The player has been removed from the transfer list and remains in your team.</p>`,
      title: 'Transfer Auction Failed',
      category: NotificationCategory.TRANSFERS,
    };
    return this.sendNotification(notification);
  }

  public auctionWon(winningTeam: Team, playerName: string) {
    const notification: Notification = {
      subject: `You won the auction for ${playerName}!`,
      content: `<p>Congratulations, you were the highest bidder for ${playerName} and the player has now been transfered to ${winningTeam.teamName}.</p>
              <p>The player has expressed their joy at joining ${winningTeam.teamName}, a club they have supported since you agreed to pay them to kick a ball around.</p>`,
      title: 'Fresh Meat',
      category: NotificationCategory.TRANSFERS,
    };
    return this.sendNotification(notification);
  }

  public playerSold(winningTeam: Team, playerName: string, receivedFee: number) {
    const notification: Notification = {
      subject: `Your player ${playerName} has been sold!`,
      content: `<p>Your player ${playerName} has been sold to ${winningTeam.teamName} for a fee of £${receivedFee}. After agent fees, we will receive ${receivedFee * 0.8}.</p>
              <p>We wish them the best of luck in their new adventure and hope the door doesn't hit them on the way out.</p>`,
      title: 'Kerching!',
      category: NotificationCategory.TRANSFERS,
    };
    return this.sendNotification(notification);
  }

  public counterOfferMade(transferRequest: DBTransferRequest, counterOfferValue: number) {
    const notification: Notification = {
      subject: `Counter offer made for ${transferRequest.player.firstName} ${transferRequest.player.surname}`,
      content: `<p>A counter-offer has been made for ${transferRequest.player.firstName} ${transferRequest.player.surname}.</p>
              <p>The counter-offer is for £${counterOfferValue}.</p>`,
      title: 'Counter Offer Made',
      category: NotificationCategory.TRANSFERS,
    };
    return this.sendNotification(notification);
  }
}
