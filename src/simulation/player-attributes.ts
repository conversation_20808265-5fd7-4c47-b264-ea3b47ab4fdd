import { BaseAttributeName } from '@/types/attribute-utils.js';
import { Possibility } from './match-events.js';
import { GamePlayer, MatchArea, Team } from './types.js';

/**
 * Calculate how much of an advantage a player has over their opposition player
 * Compares the positive effect attribute of one player against the negative effect
 * attribute of the other player. A players current energy levels also have a 40% impact
 * @param event
 * @param playerInPossession
 * @param oppositionPlayer
 */
export function calculatePlayerAdvantage(
  event: Possibility,
  playerInPossession: GamePlayer,
  oppositionPlayer: GamePlayer
): number {
  // Calculate player stats advantage
  const playerPoints =
    event.positiveAttributes.reduce((sum: number, attr: BaseAttributeName) => {
      if (!playerInPossession.player.attributes[`${attr}Current`]) {
        throw new Error(
          `Attribute ${attr} not found for player ${playerInPossession.player.playerId}`
        );
      }
      return sum + playerInPossession.player.attributes[`${attr}Current`];
    }, 0) / event.positiveAttributes.length;

  const oppositionPoints =
    event.negativeAttributes.reduce((sum, attr: BaseAttributeName) => {
      return sum + oppositionPlayer.player.attributes[`${attr}Current`];
    }, 0) / event.negativeAttributes.length;

  // Calculate energy factor (normalized to 0-1)
  const playerEnergyFactor = playerInPossession.player.energy / 100;
  const oppositionEnergyFactor = oppositionPlayer.player.energy / 100;

  // Combine stats and energy with weights (60% stats, 40% energy)
  const weightedPlayerAdvantage = playerPoints * 0.6 + playerEnergyFactor * 0.4;
  const weightedOppositionAdvantage = oppositionPoints * 0.6 + oppositionEnergyFactor * 0.4;

  // Return the final advantage
  return weightedPlayerAdvantage - weightedOppositionAdvantage;
}

export function getPlayersInPosition(
  currentPlayer: string,
  team: Team,
  matchArea: MatchArea,
  retry: number = 0
): GamePlayer[] {
  if (retry > 3) {
    throw new Error('Too many retries');
  }
  if (matchArea === MatchArea.GOAL_KICK || matchArea === MatchArea.SHOT) {
    return [team.players[0]! as unknown as GamePlayer];
  }
  if (matchArea === MatchArea.DEFENCE) {
    const players = (team.players as unknown as GamePlayer[]).filter(
      (player, index) =>
        index >= 1 &&
        index <= 4 &&
        player.player.playerId !== currentPlayer &&
        player.stats.redCards === 0 &&
        !player.isInjured

    );
    return players.length > 0
      ? players
      : getPlayersInPosition(currentPlayer, team, MatchArea.MIDFIELD, retry + 1);
  }
  if (matchArea === MatchArea.MIDFIELD) {
    const players = (team.players as unknown as GamePlayer[]).filter(
      (player, index) =>
        index >= 5 &&
        index <= 8 &&
        player.player.playerId !== currentPlayer &&
        player.stats.redCards === 0&&
        !player.isInjured
    );
    return players.length > 0
      ? players
      : getPlayersInPosition(currentPlayer, team, MatchArea.DEFENCE, retry + 1);
  }
  if (matchArea === MatchArea.ATTACK) {
    const players = (team.players as unknown as GamePlayer[]).filter(
      (player, index) =>
        index >= 9 &&
        index <= 10 &&
        player.player.playerId !== currentPlayer &&
        player.stats.redCards === 0&&
        !player.isInjured
    );
    return players.length > 0
      ? players
      : getPlayersInPosition(currentPlayer, team, MatchArea.MIDFIELD, retry + 1);
  }
  throw new Error('Invalid match area');
}
