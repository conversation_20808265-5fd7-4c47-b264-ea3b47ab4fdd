import { MatchEvent, MatchStats } from '@/model/fixture.js';
import { CommentaryManager } from '@/simulation/commentary.js';
import { NegativeOutcome } from '@/simulation/event-outcomes.js';
import { logger } from '@/utils/logger.js';
import { seededRandom, seededRandomIntInRange } from '@/utils/seeded-random.js';
import { matchAreaPossibilities, Possibility } from './match-events.js';
import { calculatePlayerAdvantage, getPlayersInPosition } from './player-attributes.js';
import { StatsCalculator } from './stats-calculator.js';
import { GamePlayer, MatchArea, NextEvent, SimulationResult, Team } from './types.js';

export class MatchEngine {
  private statsCalculator: StatsCalculator;
  private commentaryManager: CommentaryManager;
  private events: MatchEvent[] = [];
  private minute = 0;
  private half = 0;

  constructor(
    private homeTeam: Team,
    private awayTeam: Team,
    private maximumEventsPerMinute: number = 4
  ) {
    this.statsCalculator = new StatsCalculator();
    this.commentaryManager = CommentaryManager.getInstance();
  }

  private log(message: string) {
    logger.local(message);
  }

  public simulate(): SimulationResult {
    const teamStartGameInPossession = seededRandom() > 0.5 ? 0 : 1;

    // Simulate each half
    for (let half = 1; half <= 2; half++) {
      this.half = half;
      this.simulateHalf(half, teamStartGameInPossession);
    }

    return {
      stats: this.statsCalculator.getStats(),
      events: this.events,
    };
  }

  private simulateHalf(half: number, teamStartGameInPossession: number) {
    const halfLength = 45 + Math.floor(seededRandom() * 6);
    this.log(`Half ${half} begins`);

    const matchArea = MatchArea.KICK_OFF;
    const teamInPossession = half === 1 ? teamStartGameInPossession : 1 - teamStartGameInPossession;
    const playerInPossession = this.getKickOffTaker(teamInPossession);

    let nextEvent: NextEvent | null = {
      matchArea: matchArea,
      teamInPossession: teamInPossession,
      playerInPossession: playerInPossession,
    };
    const randomTeamInPossession = seededRandomIntInRange(0, 1);
    for (let minute = 1; minute <= halfLength; minute++) {
      this.minute = minute;
      nextEvent = this.simulateMinute(
        minute,
        nextEvent?.matchArea ?? MatchArea.MIDFIELD,
        nextEvent?.teamInPossession ?? randomTeamInPossession,
        nextEvent?.playerInPossession ?? this.getKickOffTaker(randomTeamInPossession)
      );
    }

    const stats = this.statsCalculator.getStats();
    if (half === 1) {
      this.addMatchEvent('HALF_TIME', {
        stats,
        homeTeam: this.homeTeam.teamId,
        awayTeam: this.awayTeam.teamId,
        homeScore: stats.score[0]?.toString(),
        awayScore: stats.score[1]?.toString(),
      });
    } else {
      this.addMatchEvent('FULL_TIME', {
        stats,
        homeTeam: this.homeTeam.teamId,
        awayTeam: this.awayTeam.teamId,
        homeScore: stats.score[0]?.toString(),
        awayScore: stats.score[1]?.toString(),
      });
    }
  }

  private addMatchEvent(
    stringID: string,
    substitutions: {
      team?: string;
      homeTeam?: string;
      oppTeam?: string;
      awayTeam?: string;
      player?: string;
      nextPlayer?: string;
      oppPlayer?: string;
      homeScore?: string;
      awayScore?: string;
      stats?: MatchStats;
    }
  ): void {
    const newEvent = {
      localisationId: stringID,
      substitutions: {
        team: substitutions.team,
        homeTeam: substitutions.homeTeam,
        oppTeam: substitutions.oppTeam,
        awayTeam: substitutions.awayTeam,
        player: substitutions.player,
        nextPlayer: substitutions.nextPlayer,
        oppPlayer: substitutions.oppPlayer,
        homeScore: substitutions.homeScore,
        awayScore: substitutions.awayScore,
      },
      minute: this.minute,
      half: this.half,
    };
    this.events.push(newEvent);
    this.log(this.commentaryManager.getText(stringID, newEvent.substitutions));
  }

  private simulateMinute(
    minute: number,
    matchArea: MatchArea,
    teamInPossession: number,
    playerInPossession: GamePlayer
  ): NextEvent | null {
    if (matchArea !== MatchArea.KICK_OFF && seededRandom() > 0.66) {
      this.log(`Minute ${minute > 45 ? `45+${minute - 45}` : minute}: Nothing happens`);
      return null;
    }

    this.log(
      `Minute ${minute > 45 ? `45+${minute - 45}` : minute}: ${matchArea} - Team ${
        teamInPossession === 0 ? this.homeTeam.teamName : this.awayTeam.teamName
      }`
    );

    const teams = [this.homeTeam, this.awayTeam];

    const newEvent = this.simulateEvent(matchArea, teamInPossession, playerInPossession, teams);

    this.checkForSubstitutions(minute);

    return newEvent;
  }

  private simulateEvent(
    matchArea: MatchArea,
    teamInPossession: number,
    playerInPossession: GamePlayer,
    teams: Team[],
    eventNumber = 0
  ): NextEvent {
    const possibilities = matchAreaPossibilities.find((p) => p.matchArea === matchArea);
    if (!possibilities) {
      throw new Error(`Match area not found ${matchArea}`);
    }

    const randomValue = seededRandom();
    const event = possibilities.possibilities.find((_p, index, arr) => {
      const cumulativeProbability = arr
        .slice(0, index + 1)
        .reduce((acc, curr) => acc + curr.probability, 0);
      return randomValue < cumulativeProbability;
    });

    if (!event) {
      throw new Error('Event not found');
    }

    // Get all players in target position for in possession team
    const possibleTargetPlayers = getPlayersInPosition(
      playerInPossession.player.playerId,
      teams[teamInPossession]!,
      event.targetArea
    );
    const targetPlayer =
      possibleTargetPlayers[seededRandomIntInRange(0, possibleTargetPlayers.length - 1)]!;

    // This event always succeeds so no need to simulate anything
    if (matchArea === MatchArea.KICK_OFF) {
      this.addMatchEvent('KICK_OFF', {
        team: teams[teamInPossession]!.teamId,
        oppTeam: teams[1 - teamInPossession]!.teamId,
        player: playerInPossession.player.playerId,
        nextPlayer: targetPlayer.player.playerId,
        oppPlayer: '',
      });

      return {
        matchArea: event.targetArea,
        teamInPossession: teamInPossession,
        playerInPossession: targetPlayer,
      };
    } else {
      this.addMatchEvent(event.commentaryId, {
        player: playerInPossession.player.playerId,
        nextPlayer: targetPlayer.player.playerId,
        team: teamInPossession === 0 ? this.homeTeam.teamId : this.awayTeam.teamId,
        oppTeam: teamInPossession === 0 ? this.awayTeam.teamId : this.homeTeam.teamId,
      });
    }

    // Get all players in target position for opposition team
    const possibleOppositionPlayers = getPlayersInPosition(
      '',
      teams[1 - teamInPossession]!,
      event.targetArea
    );

    // pick a random opposition player to challenge for the ball
    const oppositionPlayer =
      possibleOppositionPlayers[seededRandomIntInRange(0, possibleOppositionPlayers.length - 1)]!;

    const playerTeamRedCards = this.statsCalculator.getStats().redCards[teamInPossession] || 0;
    const opposingTeamRedCards =
      this.statsCalculator.getStats().redCards[1 - teamInPossession] || 0;

    const hasPlayerTeamInjury = teams[teamInPossession].players
      .slice(0, 11)
      .some((p) => p.isInjured);
    const hasOpposingTeamInjury = teams[1 - teamInPossession].players
      .slice(0, 11)
      .some((p) => p.isInjured);

    // Energy cost is doubled for teams with red cards
    playerInPossession.player.energy -= playerTeamRedCards > 0 || hasPlayerTeamInjury ? 2 : 1;
    oppositionPlayer.player.energy -= opposingTeamRedCards > 0 || hasOpposingTeamInjury ? 2 : 1;

    // Ensure energy doesn't go below zero
    playerInPossession.player.energy = Math.max(0, playerInPossession.player.energy);
    oppositionPlayer.player.energy = Math.max(0, oppositionPlayer.player.energy);

    const playerAdvantage = calculatePlayerAdvantage(event, playerInPossession, oppositionPlayer);

    // Base threshold starts at 0.5 (50% chance)
    // Player advantage can only influence up to ±0.35 (70% of the outcome)
    const PLAYER_INFLUENCE = 0.35;
    const maxAttributeDiff = 40;
    let successThreshold = 0.5 - (playerAdvantage / maxAttributeDiff) * PLAYER_INFLUENCE;

    // Make shots harder to convert
    if (event.targetArea === MatchArea.SHOT) {
      successThreshold += 0.15; // Add 15% more difficulty for shots
    }

    if (randomValue > successThreshold) {
      this.statsCalculator.updateMatchStats({
        event,
        teamInPossession,
        matchArea: event.targetArea,
      });

      const successEvent = this.eventSuccess(
        event,
        teams,
        playerInPossession,
        teamInPossession,
        possibleTargetPlayers
      );

      this.statsCalculator.updatePlayerStats({
        playerStats: playerInPossession.stats,
        oppositionPlayerStats: oppositionPlayer.stats,
        event,
      });

      if (eventNumber < this.maximumEventsPerMinute) {
        return this.simulateEvent(
          successEvent.matchArea,
          successEvent.teamInPossession,
          successEvent.playerInPossession,
          teams,
          eventNumber + 1
        );
      }
      return successEvent;
    }

    // otherwise opposition player wins the ball
    const { matchEvent: failureEvent, negativeOutcome } = this.eventFailure(
      playerInPossession,
      oppositionPlayer,
      targetPlayer,
      event,
      teamInPossession
    );

    this.statsCalculator.updateMatchStats({
      event,
      negativeOutcome,
      teamInPossession,
      matchArea: event.targetArea,
    });

    this.statsCalculator.updatePlayerStats({
      playerStats: playerInPossession.stats,
      oppositionPlayerStats: oppositionPlayer.stats,
      event,
      negativeOutcome,
    });

    if (!failureEvent.playerInPossession) {
      failureEvent.playerInPossession =
        possibleTargetPlayers[seededRandomIntInRange(0, possibleTargetPlayers.length - 1)]!;
    }

    if (oppositionPlayer.stats.yellowCards === 2) {
      oppositionPlayer.stats.redCards++;
      this.statsCalculator.getStats().redCards[1 - teamInPossession]!++;
    }

    if (eventNumber < this.maximumEventsPerMinute) {
      return this.simulateEvent(
        failureEvent.matchArea,
        failureEvent.teamInPossession,
        failureEvent.playerInPossession,
        teams,
        eventNumber + 1
      );
    }

    return failureEvent;
  }

  private getKickOffTaker(teamInPossession: number): GamePlayer {
    const team = teamInPossession === 0 ? this.homeTeam : this.awayTeam;
    const players = team.players;

    // First try to get a midfielder
    const midfielders = players.filter((_, index) => index >= 5 && index <= 8);
    if (midfielders.length > 0) {
      // Get a random midfielder
      return midfielders[Math.floor(seededRandom() * midfielders.length)]!;
    }

    // If no midfielders, get any outfield player
    const outfieldPlayers = players.filter((_, index) => index > 0 && index <= 10);
    if (outfieldPlayers.length > 0) {
      return outfieldPlayers[Math.floor(seededRandom() * outfieldPlayers.length)]!;
    }

    logger.error('No players found', {
      teamInPossession,
      players,
    });
    throw new Error('No players found');
  }

  private eventSuccess(
    event: Possibility,
    teams: Team[],
    playerInPossession: GamePlayer,
    teamInPossession: number,
    possibleTargetPlayers: GamePlayer[]
  ): { matchArea: MatchArea; teamInPossession: number; playerInPossession: GamePlayer } {
    if (event.targetArea === MatchArea.SHOT) {
      const score = this.statsCalculator.goalScored(
        teamInPossession,
        playerInPossession.player.playerId,
        playerInPossession.player.surname,
        this.minute,
        this.half
      );
      this.addMatchEvent('GOAL', {
        team: teams[teamInPossession]!.teamId,
        oppTeam: teams[1 - teamInPossession]!.teamId,
        homeTeam: teams[0]!.teamId,
        awayTeam: teams[1]!.teamId,
        player: playerInPossession.player.playerId,
        homeScore: score[0]?.toString(),
        awayScore: score[1]?.toString(),
      });
      return {
        matchArea: MatchArea.KICK_OFF,
        teamInPossession: 1 - teamInPossession, // Other team kicks off after a goal
        playerInPossession: this.getKickOffTaker(1 - teamInPossession),
      };
    }

    // For non-shot events, pick a new player in the target area
    return {
      matchArea: event.targetArea,
      teamInPossession: teamInPossession,
      playerInPossession:
        possibleTargetPlayers[seededRandomIntInRange(0, possibleTargetPlayers.length - 1)]!,
    };
  }

  private eventFailure(
    playerInPossession: GamePlayer,
    oppositionPlayer: GamePlayer,
    nextPlayer: GamePlayer,
    event: Possibility,
    teamInPossession: number
  ): { matchEvent: NextEvent; negativeOutcome: NegativeOutcome } {
    // calculate a random negative outcome
    const randomValue = seededRandom();
    const negativeOutcomes = event.negativeOutcomes || [];
    const negativeOutcome = negativeOutcomes.find((_o, index, arr) => {
      const cumulativeProbability = arr
        .slice(0, index + 1)
        .reduce((acc, curr) => acc + curr.probability, 0);
      return randomValue < cumulativeProbability;
    });
    if (!negativeOutcome) {
      throw new Error('Negative outcome not found');
    }

    if (negativeOutcome.description.toLowerCase().includes('foul')) {
      // set the target area to the free kick type based on the match area
      switch (event.targetArea) {
        case MatchArea.DEFENCE:
          negativeOutcome.targetArea = MatchArea.DEFENDING_FREE_KICK;
          break;
        case MatchArea.MIDFIELD:
          negativeOutcome.targetArea = MatchArea.MIDFIELD_FREE_KICK;
          break;
        case MatchArea.ATTACK:
          negativeOutcome.targetArea = MatchArea.ATTACKING_FREE_KICK;
          break;
      }
    }

    this.addMatchEvent(negativeOutcome.commentaryId, {
      player: playerInPossession.player.playerId,
      oppPlayer: oppositionPlayer.player.playerId,
      nextPlayer: nextPlayer ? nextPlayer.player.playerId : undefined,
      team: teamInPossession === 0 ? this.homeTeam.teamId : this.awayTeam.teamId,
      oppTeam: teamInPossession === 0 ? this.awayTeam.teamId : this.homeTeam.teamId,
    });

    // Check for potential injury
    const injuredPlayer = this.checkForInjury(
      playerInPossession,
      oppositionPlayer,
      negativeOutcome
    );
    if (injuredPlayer) {
      injuredPlayer.isInjured = true;
      const injuredTeamIndex =
        injuredPlayer === playerInPossession ? teamInPossession : 1 - teamInPossession;
      const injuredTeam = injuredTeamIndex === 0 ? this.homeTeam : this.awayTeam;

      this.addMatchEvent('INJURY', {
        team: injuredTeam.teamId,
        player: injuredPlayer.player.playerId,
      });

      this.handleInjury(injuredTeam, injuredPlayer);
    }

    if (negativeOutcome?.noPossessionLost) {
      this.log('Possession retained');
      return {
        matchEvent: {
          matchArea: negativeOutcome?.targetArea ?? event.targetArea,
          teamInPossession: teamInPossession,
          playerInPossession: null,
        },
        negativeOutcome: negativeOutcome,
      };
    }
    return {
      matchEvent: {
        matchArea:
          negativeOutcome?.targetArea ?? this.getOppositionAreaFromMatchArea(event.targetArea),
        teamInPossession: 1 - teamInPossession,
        playerInPossession: oppositionPlayer,
      },
      negativeOutcome: negativeOutcome,
    };
  }

  private getOppositionAreaFromMatchArea(matchArea: MatchArea): MatchArea {
    if (matchArea === MatchArea.DEFENCE) {
      return MatchArea.ATTACK;
    }
    if (matchArea === MatchArea.MIDFIELD) {
      return MatchArea.MIDFIELD;
    }
    if (matchArea === MatchArea.ATTACK) {
      return MatchArea.DEFENCE;
    }
    if (matchArea === MatchArea.SHOT) {
      return MatchArea.GOAL_KICK;
    }
    throw new Error('Invalid match area');
  }

  private checkForSubstitutions(minute: number): void {
    [this.homeTeam, this.awayTeam].forEach((team) => {
      // Only check active players (0-10)
      const activePlayers = team.players.slice(0, 11);
      const substitutes = team.players.slice(11, 16);

      // Find players with low energy
      const exhaustedPlayers = activePlayers.filter(
        (player) =>
          ((player.player.energy < 40 && minute > 40) || player.player.energy < 10) &&
          !player.hasBeenSubbed
      );

      if (exhaustedPlayers.length === 0 || substitutes.length === 0) return;

      // Limit subs to 3 per team per game (or whatever limit you want)
      const subsUsed = team.players.filter((p) => p.hasBeenSubbed).length;
      const maxSubs = 3;

      // Select one player to substitute this minute (avoid multiple subs at once)
      const playerToSub = exhaustedPlayers[0];
      if (subsUsed < maxSubs && playerToSub) {
        this.substitutePlayer(team, playerToSub, substitutes);
      }
    });
  }

  private substitutePlayer(
    team: Team,
    exhaustedPlayer: GamePlayer,
    substitutes: GamePlayer[]
  ): void {
    // Find a substitute with similar position
    const position = team.players.indexOf(exhaustedPlayer);
    let bestSubstitute = substitutes[0]!;

    // Try to match by position type (defender, midfielder, forward)
    if (position > 0 && position <= 4) {
      // Defender - find a defensive substitute
      const defensiveSubs = substitutes.filter((s) => !s.hasBeenSubbed);
      if (defensiveSubs.length > 0) bestSubstitute = defensiveSubs[0]!;
    } else if (position >= 5 && position <= 8) {
      // Midfielder - find a midfield substitute
      const midfieldSubs = substitutes.filter((s) => !s.hasBeenSubbed);
      if (midfieldSubs.length > 0) bestSubstitute = midfieldSubs[0]!;
    } else if (position >= 9 && position <= 10) {
      // Forward - find an attacking substitute
      const attackingSubs = substitutes.filter((s) => !s.hasBeenSubbed);
      if (attackingSubs.length > 0) bestSubstitute = attackingSubs[0]!;
    }

    // Find the substitute's position in the array
    const substitutePosition = team.players.indexOf(bestSubstitute);

    // Swap the players completely (exchange their positions)
    team.players[substitutePosition] = exhaustedPlayer;
    team.players[position] = bestSubstitute;

    // Mark as subbed
    exhaustedPlayer.hasBeenSubbed = true;
    bestSubstitute.hasBeenSubbed = true;

    // Log the substitution
    this.addMatchEvent('SUBSTITUTION', {
      team: team.teamId,
      player: exhaustedPlayer.player.playerId,
      nextPlayer: bestSubstitute.player.playerId,
    });

    this.log(
      `Substitution for ${team.teamName}: ${exhaustedPlayer.player.surname} OFF, ${bestSubstitute.player.surname} ON`
    );
  }

  private checkForInjury(
    playerInPossession: GamePlayer,
    oppositionPlayer: GamePlayer,
    negativeOutcome: NegativeOutcome
  ): GamePlayer | null {
    // Higher chance on fouls, especially from players who already have cards
    let injuryChance = 0.01; // Base chance

    if (negativeOutcome.description.toLowerCase().includes('foul')) {
      injuryChance = 0.05; // Higher chance on fouls

      if (oppositionPlayer.stats.yellowCards > 0) {
        injuryChance = 0.1; // Even higher for reckless players
      }
    }

    if (seededRandom() < injuryChance) {
      // Usually the player receiving the foul gets injured
      return seededRandom() < 0.9 ? playerInPossession : oppositionPlayer;
    }

    return null;
  }

  private handleInjury(team: Team, injuredPlayer: GamePlayer): void {
    const position = team.players.indexOf(injuredPlayer);
    const substitutes = team.players.slice(11, 16).filter((p) => !p.hasBeenSubbed);
    const subsUsed = team.players.filter((p) => p.hasBeenSubbed).length / 2; // Division by 2 because we mark both players
    const maxSubs = 3;

    this.log(`${injuredPlayer.player.surname} is injured!`);

    if (subsUsed < maxSubs && substitutes.length > 0) {
      // Make a substitution for the injured player
      this.substitutePlayer(team, injuredPlayer, substitutes);
    } else {
      this.log(
        `${injuredPlayer.player.surname} has to continue despite the injury as no substitutions remain.`
      );
      // Player remains injured and on field
    }
  }
}
