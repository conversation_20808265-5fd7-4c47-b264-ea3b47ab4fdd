import { Player } from '@/entities/Player.js';
import { Team } from '@/entities/Team.js';
import { CurrentAttributes } from '@/model/player.js';

export interface LeagueStandings {
  played: number;
  points: number;
  goalsFor: number;
  goalsAgainst: number;
  wins: number;
  draws: number;
  losses: number;
}

export interface DBTeam {
  gameworldId: string;
  leagueId: string;
  tier: number;
  teamId: string;
  managerId?: string;
  teamName: string;
  balance: number;
  standings: LeagueStandings;
  selectionOrder: string[]; // Player IDs in the order the player has selected them
}

// Interface for player response with current attributes only
export type PlayerResponse = Omit<Player, 'attributes' | 'team'> & {
  attributes: CurrentAttributes;
};

export type GetTeamResponse = Omit<Team, 'players'> & {
  players: PlayerResponse[];
  nextFixture?: {
    fixtureId: string;
    date: number;
  };
};

export interface DBAvailableTeam {
  gameworldId: string;
  teamId: string;
}
