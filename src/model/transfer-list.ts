import { CurrentAttributes } from '@/model/player.js';

/**
 * Query parameters for transfer list API endpoints
 */
export interface TransferListQueryParameters {
  limit?: string;
  lastEvaluatedKey?: string;
}

/**
 * Path parameters for transfer list API endpoints
 */
export interface TransferListPathParameters {
  gameworldId: string;
}

/**
 * Bid history entry in the response
 */
export type BidHistoryResponse = {
  teamId: string;
  teamName: string;
  maximumBid: number;
  bidTime: number;
};

/**
 * Transfer listed player with current attributes only
 */
export type TransferListedPlayerResponse = {
  gameworldId: string;
  teamId: string;
  leagueId: string;
  playerId: string;
  firstName: string;
  surname: string;
  attributes: CurrentAttributes;
  age: number;
  value: number;
  auctionStartPrice: number;
  auctionCurrentPrice: number;
  auctionEndTime: number;
  bidHistory: BidHistoryResponse[];
};

/**
 * Response format for transfer list API endpoints
 */
export interface TransferListResponse {
  players: TransferListedPlayerResponse[];
  lastEvaluatedKey?: string;
}
