{"name": "jfg-infrastructure", "main": "index.ts", "scripts": {"deploy": "pulumi up --yes --non-interactive --skip-preview --refresh --stack dev", "deploy-local-quick": "pulumi up --yes --non-interactive --skip-preview --stack dev", "deploy-aws": "pulumi up --stack stage --refresh", "deploy-aws-quick": "pulumi up --stack stage --yes --non-interactive --skip-preview", "destroy-aws": "pulumi down --stack stage"}, "devDependencies": {"@types/node": "^20"}, "dependencies": {"@pulumi/aws": "^6.68.0", "@pulumi/awsx": "^2.21.0", "@pulumi/pulumi": "^3.113.0"}}