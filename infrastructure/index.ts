import * as aws from '@pulumi/aws';
import { addGlobalQueuePermission, alarmEmail, setGlobalEnvVar, stageName } from './config';
import { createLambdaFunction } from './lambda';
import { subscribeEmailToAlarmTopic } from './queueMonitoring';
import { createApiRoutes } from './resources/api';
import { createDatabaseTables } from './resources/database';
import { createDevResources } from './resources/dev';
import { createEmailResources } from './resources/email';
import { createFixtureResources } from './resources/fixtures';
import { createGameworldResources } from './resources/gameworld';
import { createLeagueResources } from './resources/league';
import { createManagerResources } from './resources/manager';
import { createPlayerResources } from './resources/player';
import { createScoutingResources } from './resources/scouting';
import { createTeamResources } from './resources/team';
import { createTransferResources } from './resources/transfers';
import { addSesRoleToLambda, createSesResources, outputSesVerificationRecords } from './ses';

// Create SES resources for email sending
const { domainIdentity, domainDkim, domainMailFrom, emailIdentity } = createSesResources();

// Output SES verification records for DNS configuration
if (stageName !== 'dev') {
  outputSesVerificationRecords(domainIdentity!, domainDkim!, domainMailFrom!);
}

// Create DynamoDB Tables
const {
  leaguesTable,
  teamsTable,
  playersTable,
  transferListedPlayersTable,
  availableTeamsTable,
  managersTable,
  fixturesTable,
  scoutingRequestsTable,
  scoutedPlayersTable,
} = createDatabaseTables();

// Create email processing resources
const {
  emailQueue,
  emailQueueUrl,
  processEmailLambda,
  errorAlarmTopic: emailErrorAlarmTopic,
} = createEmailResources();

// Set up global Lambda configuration
// Set the email queue URL as a global environment variable
// This will be automatically included in all Lambda functions
setGlobalEnvVar('EMAIL_QUEUE_URL', emailQueueUrl);

// Add global permission for all Lambda functions to send messages to the email queue
// This will be automatically included in all Lambda function roles
addGlobalQueuePermission('email', emailQueue, 'send');

// Create HTTP Lambda Functions

// Create a role with SES permissions for the ping Lambda
const pingRole = addSesRoleToLambda('ping');

// Create the ping Lambda with the SES role
const [pingLambda] = createLambdaFunction(
  'ping',
  '../dist/ping/ping',
  'index.handler',
  undefined,
  pingRole,
  [
    {
      source: '../dist/templates/basic.mjml',
      destination: 'templates/basic.mjml',
    },
  ]
);

const {
  gameworldLambda,
  endOfSeasonQueue,
  teamQueue,
  unattachedPlayersQueue,
  errorAlarmTopic: gameworldErrorAlarmTopic,
  generateFixturesLambda,
} = createGameworldResources({
  leaguesTable,
  teamsTable,
});

const {
  playerQueue,
  getTeamLambda,
  updateTeamOrderLambda,
  errorAlarmTopic: teamErrorAlarmTopic,
} = createTeamResources({
  teamsTable,
  availableTeamsTable,
  fixturesTable,
  managersTable,
  playersTable,
  teamQueue,
  generateFixturesLambda,
});

const {
  getTransferListPlayersLambda,
  getMyBidTransferListPlayersLambda,
  errorAlarmTopic: playerErrorAlarmTopic,
} = createPlayerResources({
  playersTable,
  transferListedPlayersTable,
  playerQueue,
  unattachedPlayersQueue,
});

const {
  getManagerLambda,
  postConfirmationLambda,
  updateManagerNameLambda,
  updateNotificationPreferencesLambda,
} = createManagerResources({
  managersTable,
  availableTeamsTable,
});

const { getLeaguesLambda, getLeagueLambda } = createLeagueResources({ leaguesTable, teamsTable });

const { requestScoutingLambda, getScoutedPlayersLambda, errorAlarmTopic } = createScoutingResources(
  {
    scoutingRequestsTable,
    scoutedPlayersTable,
    managersTable,
    teamsTable,
    playersTable,
    getManagerLambda,
  }
);

const {
  getFixturesLambda,
  getTeamFixturesLambda,
  getFixtureLambda,
  fixtureQueue,
  getCommentaryLambda,
  errorAlarmTopic: fixtureErrorAlarmTopic,
} = createFixtureResources({
  fixturesTable,
  teamsTable,
  playersTable,
  getTeamLambda,
});

const {
  submitTransferOfferLambda,
  submitBidLambda,
  myActiveTransfersLambda,
  errorAlarmTopic: transferErrorAlarmTopic,
} = createTransferResources({
  transferListedPlayersTable,
  emailQueue,
  unattachedPlayersQueue,
});

const localOnlyRoutes = createDevResources({
  managersTable,
  availableTeamsTable,
  getFixturesLambda,
  fixtureQueue,
  endOfSeasonQueue,
});

// First create the role
const xrayRole = new aws.iam.Role(`${stageName}-xray-role`, {
  assumeRolePolicy: JSON.stringify({
    Version: '2012-10-17',
    Statement: [
      {
        Action: 'sts:AssumeRole',
        Principal: {
          Service: ['lambda.amazonaws.com', 'apigateway.amazonaws.com'],
        },
        Effect: 'Allow',
      },
    ],
  }),
});

// Then attach the policy to the role
new aws.iam.RolePolicy(`${stageName}-xray-policy`, {
  role: xrayRole.id, // Use the role's id here, not the name
  policy: JSON.stringify({
    Version: '2012-10-17',
    Statement: [
      {
        Effect: 'Allow',
        Action: [
          'xray:PutTraceSegments',
          'xray:PutTelemetryRecords',
          'xray:GetSamplingRules',
          'xray:GetSamplingTargets',
          'xray:GetSamplingStatisticSummaries',
        ],
        Resource: ['*'],
      },
    ],
  }),
});

const { api, cognitoUserPoolId, cognitoUserPoolClientId } = createApiRoutes({
  pingLambda,
  gameworldLambda,
  postConfirmationLambda,
  getLeaguesLambda,
  getLeagueLambda,
  getManagerLambda,
  updateManagerNameLambda,
  updateNotificationPreferencesLambda,
  getTeamLambda,
  updateTeamOrderLambda,
  getTransferListPlayersLambda,
  getMyBidTransferListPlayersLambda,
  getFixturesLambda,
  getTeamFixturesLambda,
  getFixtureLambda,
  getCommentaryLambda,
  requestScoutingLambda,
  getScoutedPlayersLambda,
  submitTransferOfferLambda,
  submitBidLambda,
  myActiveTransfersLambda,
  localOnlyRoutes,
});

// Export the URL of the API
export const url = api.stage.invokeUrl;

// Export the Cognito IDs as stack outputs
export const userPoolId = cognitoUserPoolId;
export const userPoolClientId = cognitoUserPoolClientId;

// Export SES resources
export const sesEmailIdentity = emailIdentity?.email;
export const sesDomainIdentity = domainIdentity?.domain;

// Subscribe email to error alarm topics if email is provided
if (alarmEmail) {
  // Subscribe to all error alarm topics
  subscribeEmailToAlarmTopic('scouting-error-logs', errorAlarmTopic, alarmEmail);
  subscribeEmailToAlarmTopic('team-error-logs', teamErrorAlarmTopic, alarmEmail);
  subscribeEmailToAlarmTopic('player-error-logs', playerErrorAlarmTopic, alarmEmail);
  subscribeEmailToAlarmTopic('gameworld-error-logs', gameworldErrorAlarmTopic, alarmEmail);
  subscribeEmailToAlarmTopic('fixture-error-logs', fixtureErrorAlarmTopic, alarmEmail);
  subscribeEmailToAlarmTopic('email-error-logs', emailErrorAlarmTopic, alarmEmail);
  subscribeEmailToAlarmTopic('transfer-error-logs', transferErrorAlarmTopic, alarmEmail);
}
