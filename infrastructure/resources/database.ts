import { createDynamoDbTable } from '../dynamodb';

export function createDatabaseTables() {
  const leaguesTable = createDynamoDbTable('leaguesTable', {
    attributes: [
      { name: 'gameworldId', type: 'S' },
      { name: 'id', type: 'S' },
    ],
    hashKey: 'gameworldId',
    rangeKey: 'id',
    billingMode: 'PAY_PER_REQUEST',
  });
  const teamsTable = createDynamoDbTable('teamsTable', {
    attributes: [
      { name: 'gameworldId', type: 'S' },
      { name: 'teamId', type: 'S' },
      { name: 'leagueId', type: 'S' },
      { name: 'managerId', type: 'S' },
    ],
    hashKey: 'gameworldId',
    rangeKey: 'teamId',
    globalSecondaryIndexes: [
      {
        name: 'leagueIndex',
        hashKey: 'gameworldId',
        rangeKey: 'leagueId',
        projectionType: 'ALL',
      },
      {
        name: 'managerIndex',
        hashKey: 'managerId',
        projectionType: 'ALL',
      },
    ],
    billingMode: 'PAY_PER_REQUEST',
  });
  const playersTable = createDynamoDbTable('playersTable', {
    attributes: [
      { name: 'gameworldId', type: 'S' },
      { name: 'playerId', type: 'S' },
      { name: 'teamId', type: 'S' },
      { name: 'leagueId', type: 'S' },
    ],
    hashKey: 'gameworldId',
    rangeKey: 'playerId',
    globalSecondaryIndexes: [
      {
        name: 'teamIndex',
        hashKey: 'teamId',
        rangeKey: 'gameworldId',
        projectionType: 'ALL',
      },
      {
        name: 'leagueIndex',
        hashKey: 'leagueId',
        rangeKey: 'gameworldId',
        projectionType: 'ALL',
      },
    ],
    billingMode: 'PAY_PER_REQUEST',
  });

  const transferListedPlayersTable = createDynamoDbTable('transferListedPlayersTable', {
    attributes: [
      { name: 'gameworldId', type: 'S' },
      { name: 'playerId', type: 'S' },
    ],
    hashKey: 'gameworldId',
    rangeKey: 'playerId',
    billingMode: 'PAY_PER_REQUEST',
  });

  const availableTeamsTable = createDynamoDbTable('availableTeamsTable', {
    attributes: [{ name: 'teamId', type: 'S' }],
    hashKey: 'teamId',
    billingMode: 'PAY_PER_REQUEST',
  });
  const managersTable = createDynamoDbTable('managersTable', {
    attributes: [{ name: 'managerId', type: 'S' }],
    hashKey: 'managerId',
    billingMode: 'PAY_PER_REQUEST',
  });
  const fixturesTable = createDynamoDbTable('fixturesTable', {
    attributes: [
      { name: 'gameworldId_leagueId', type: 'S' },
      { name: 'fixtureId', type: 'S' },
    ],
    hashKey: 'gameworldId_leagueId',
    rangeKey: 'fixtureId',
    billingMode: 'PAY_PER_REQUEST',
  });
  const scoutingRequestsTable = createDynamoDbTable('scoutingRequestsTable', {
    attributes: [
      { name: 'pk', type: 'S' },
      { name: 'requestId', type: 'S' },
    ],
    hashKey: 'pk',
    rangeKey: 'requestId',
    billingMode: 'PAY_PER_REQUEST',
  });

  const scoutedPlayersTable = createDynamoDbTable('scoutedPlayersTable', {
    attributes: [
      { name: 'gameworldId', type: 'S' },
      { name: 'teamId', type: 'S' },
      { name: 'playerId', type: 'S' },
    ],
    hashKey: 'gameworldId',
    rangeKey: 'teamId',
    globalSecondaryIndexes: [
      {
        name: 'playerIndex',
        hashKey: 'playerId',
        rangeKey: 'gameworldId',
        projectionType: 'ALL',
      },
    ],
    billingMode: 'PAY_PER_REQUEST',
  });

  return {
    leaguesTable,
    teamsTable,
    playersTable,
    transferListedPlayersTable,
    availableTeamsTable,
    managersTable,
    fixturesTable,
    scoutingRequestsTable,
    scoutedPlayersTable,
  };
}
