import { Function } from '@pulumi/aws/lambda';
import * as pulumi from '@pulumi/pulumi';
import { createRESTAPI } from '../apiGateway';
import { createAuthorizerLambda } from '../authorizer';
import { createCognitoUserPool } from '../cognito';
import { stageName } from '../config';

export function createApiRoutes(resources: {
  getLeaguesLambda: Function;
  pingLambda: Function;
  gameworldLambda: Function;
  postConfirmationLambda: Function;
  getLeagueLambda: Function;
  getManagerLambda: Function;
  updateManagerNameLambda: Function;
  updateNotificationPreferencesLambda: Function;
  getTeamLambda: Function;
  updateTeamOrderLambda: Function;
  getFixturesLambda: Function;
  getTeamFixturesLambda: Function;
  getFixtureLambda: Function;
  getCommentaryLambda: Function;
  getTransferListPlayersLambda: Function;
  getMyBidTransferListPlayersLambda: Function;
  requestScoutingLambda: Function;
  getScoutedPlayersLambda: Function;
  submitTransferOfferLambda: Function;
  submitBidLambda: Function;
  myActiveTransfersLambda: Function;
  localOnlyRoutes: any;
}) {
  // Initialize outputs as undefined for dev environment
  let userPoolId: pulumi.Output<string> | undefined;
  let userPoolClientId: pulumi.Output<string> | undefined;
  let authorizerLambda;

  // not supported on localstack
  if (stageName !== 'dev') {
    const { userPool, userPoolClient } = createCognitoUserPool(
      'userPool',
      resources.postConfirmationLambda
    );
    userPoolId = userPool.id;
    userPoolClientId = userPoolClient.id;
    [authorizerLambda] = createAuthorizerLambda(userPool.id, userPoolClient.id);
  }

  const defaultAuthorizer = {
    authType: 'custom',
    authorizerName: 'lambda-authorizer',
    parameterName: 'Authorization',
    identityValidationExpression: '^Bearer [-0-9a-zA-Z._]*$',
    type: 'TOKEN',
    parameterLocation: 'header',
    authorizerResultTtlInSeconds: 300,
    handler: authorizerLambda,
  };

  const api = createRESTAPI([
    {
      path: '/ping',
      method: 'GET',
      eventHandler: resources.pingLambda,
    },
    {
      path: '/generate/gameworld',
      method: 'POST',
      eventHandler: resources.gameworldLambda,
    },
    {
      path: '/{gameworldId}/leagues',
      method: 'GET',
      eventHandler: resources.getLeaguesLambda,
    },
    {
      path: '/{gameworldId}/leagues/{leagueId}',
      method: 'GET',
      eventHandler: resources.getLeagueLambda,
    },
    {
      path: '/manager',
      method: 'GET',
      eventHandler: resources.getManagerLambda,
      authorizers: [defaultAuthorizer],
    },
    {
      path: '/manager/{managerId}',
      method: 'GET',
      eventHandler: resources.getManagerLambda,
      authorizers: [defaultAuthorizer],
    },
    {
      path: '/manager/name',
      method: 'PUT',
      eventHandler: resources.updateManagerNameLambda,
      authorizers: [defaultAuthorizer],
    },
    {
      path: '/manager/notification-preferences',
      method: 'PUT',
      eventHandler: resources.updateNotificationPreferencesLambda,
      authorizers: [defaultAuthorizer],
    },
    {
      path: '/{gameworldId}/team/{teamId}',
      method: 'GET',
      eventHandler: resources.getTeamLambda,
      authorizers: [defaultAuthorizer],
    },
    {
      path: '/{gameworldId}/team/{teamId}',
      method: 'POST',
      eventHandler: resources.updateTeamOrderLambda,
      authorizers: [defaultAuthorizer],
    },
    {
      path: '/{gameworldId}/league/{leagueId}/fixtures',
      method: 'GET',
      eventHandler: resources.getFixturesLambda,
      authorizers: [defaultAuthorizer],
    },
    {
      path: '/{gameworldId}/league/{leagueId}/fixtures/{teamId}',
      method: 'GET',
      eventHandler: resources.getTeamFixturesLambda,
      authorizers: [defaultAuthorizer],
    },
    {
      path: '/{gameworldId}/league/{leagueId}/fixture/{fixtureId}',
      method: 'GET',
      eventHandler: resources.getFixtureLambda,
      authorizers: [defaultAuthorizer],
    },
    {
      path: '/commentary',
      method: 'GET',
      eventHandler: resources.getCommentaryLambda,
    },
    {
      path: '/{gameworldId}/players/transfer-list',
      method: 'GET',
      eventHandler: resources.getTransferListPlayersLambda,
      authorizers: [defaultAuthorizer],
    },
    {
      path: '/{gameworldId}/players/my-bids',
      method: 'GET',
      eventHandler: resources.getMyBidTransferListPlayersLambda,
      authorizers: [defaultAuthorizer],
    },
    {
      path: '/scouting/request',
      method: 'POST',
      eventHandler: resources.requestScoutingLambda,
      authorizers: [defaultAuthorizer],
    },
    {
      path: '/{gameworldId}/team/{teamId}/scouted-players',
      method: 'GET',
      eventHandler: resources.getScoutedPlayersLambda,
      authorizers: [defaultAuthorizer],
    },
    {
      path: '/transfer/offer',
      method: 'POST',
      eventHandler: resources.submitTransferOfferLambda,
      authorizers: [defaultAuthorizer],
    },
    {
      path: '/transfer/bid',
      method: 'POST',
      eventHandler: resources.submitBidLambda,
      authorizers: [defaultAuthorizer],
    },
    {
      path: '/transfer/my-active',
      method: 'GET',
      eventHandler: resources.myActiveTransfersLambda,
      authorizers: [defaultAuthorizer],
    },
    ...resources.localOnlyRoutes,
  ]);

  return {
    api,
    cognitoUserPoolId: userPoolId,
    cognitoUserPoolClientId: userPoolClientId,
  };
}
