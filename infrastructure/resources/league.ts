import { Table } from '@pulumi/aws/dynamodb';
import { addDynamoPolicyToRole } from '../dynamodb';
import { createLambdaFunction } from '../lambda';

interface LeagueResourcesConfig {
  teamsTable: Table;
  leaguesTable: Table;
}

export function createLeagueResources(config: LeagueResourcesConfig) {
  let getLeaguesRole = addDynamoPolicyToRole(
    'getLeaguesHandler',
    [config.leaguesTable],
    ['dynamodb:GetItem', 'dynamodb:Query']
  );
  const [getLeaguesLambda] = createLambdaFunction(
    'getLeaguesHandler',
    '../dist/league/getLeagues',
    'index.handler',
    { LEAGUES_TABLE_NAME: config.leaguesTable.name },
    getLeaguesRole
  );

  let getLeagueRole = addDynamoPolicyToRole(
    'getLeagueHandler',
    [config.leaguesTable, config.teamsTable],
    ['dynamodb:GetItem', 'dynamodb:Query']
  );
  const [getLeagueLambda] = createLambdaFunction(
    'getLeagueHandler',
    '../dist/league/getLeague',
    'index.handler',
    { LEAGUES_TABLE_NAME: config.leaguesTable.name, TEAMS_TABLE_NAME: config.teamsTable.name },
    getLeagueRole
  );

  return { getLeaguesLambda, getLeagueLambda };
}
