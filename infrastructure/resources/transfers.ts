import { Queue } from '@pulumi/aws/sqs';
import { createLambdaErrorLogAlarm } from '../cloudwatch';
import { stageName } from '../config';
import { addDynamoPolicyToRole } from '../dynamodb';
import { createScheduledRule } from '../eventBridge';
import { createLambdaFunction } from '../lambda';
import {
  addQueueReadPolicyToRole,
  addQueueSendPolicyToRole,
  createDLQ,
  createEventSourceMapping,
  createQueue,
} from '../queue';
import { createCloudWatchAlarmTopic } from '../queueMonitoring';

interface TransferResourcesConfig {
  transferListedPlayersTable: any;
  emailQueue: Queue;
  unattachedPlayersQueue: Queue;
}

export function createTransferResources(config: TransferResourcesConfig) {
  // Create dead letter queue for AI transfers
  const aiTransfersDLQ = createDLQ('ai-transfers');

  // Create main queue for AI transfers
  const aiTransfersQueue = createQueue('ai-transfers', aiTransfersDLQ, 3, {
    visibilityTimeout: 120, // 2 minutes
    messageRetentionSeconds: 86400, // 1 day
  });

  const [submitTransferOfferLambda] = createLambdaFunction(
    'submitTransferOfferHandler',
    '../dist/transfers/submitOffer',
    'index.handler'
  );

  const [submitBidLambda] = createLambdaFunction(
    'submitBidHandler',
    '../dist/transfers/submitBid',
    'index.handler'
  );

  // Create a role with permissions to access the transfer listed players table
  let processAuctionEndRole = addDynamoPolicyToRole(
    'processAuctionEnd',
    [config.transferListedPlayersTable],
    ['dynamodb:GetItem', 'dynamodb:Query', 'dynamodb:UpdateItem', 'dynamodb:DeleteItem']
  );

  // Add permission to send messages to the unattached players queue
  // Note: Email queue permission is handled globally
  processAuctionEndRole = addQueueSendPolicyToRole(
    'processAuctionEndToUnattachedPlayersQueue',
    config.unattachedPlayersQueue,
    processAuctionEndRole
  );

  // Create an SNS topic for CloudWatch alarms
  const errorAlarmTopic = createCloudWatchAlarmTopic('transfer-error');

  // Create the Lambda function for processing auction ends
  const [processAuctionEndLambda, processAuctionEndLogGroup] = createLambdaFunction(
    'processAuctionEndHandler',
    '../dist/transfers/processAuctionEnd',
    'index.handler',
    {
      // EMAIL_QUEUE_URL is now handled globally
      UNATTACHED_PLAYERS_QUEUE_URL: config.unattachedPlayersQueue.url,
    },
    processAuctionEndRole
  );

  // Create EventBridge rule to trigger the lambda at midnight UK time
  createScheduledRule({
    name: 'process-auction-end',
    description: 'Triggers a lambda to process completed auctions at midnight',
    scheduleExpression: 'cron(30 * * * ? *)', // Half past every hour
    lambda: processAuctionEndLambda,
  });

  // Create Lambda for simulating AI transfers
  let simulateAITransfersRole = addQueueSendPolicyToRole('simulateAITransfers', aiTransfersQueue);

  const [simulateAITransfersLambda] = createLambdaFunction(
    'simulateAITransfersHandler',
    '../dist/transfers/simulateAITransfers',
    'index.handler',
    {
      AI_TRANSFERS_QUEUE_URL: aiTransfersQueue.url,
    },
    simulateAITransfersRole
  );

  // Create scheduled rule to trigger AI transfers simulation
  createScheduledRule({
    name: 'simulate-ai-transfers',
    description: 'Triggers a lambda to simulate AI transfers',
    scheduleExpression: 'cron(0 * * * ? *)', // Every hour
    lambda: simulateAITransfersLambda,
  });

  // Create Lambda for processing AI transfer requests from the queue
  let processAITransfersRole = addQueueReadPolicyToRole('processAITransfers', aiTransfersQueue);

  // Create the Lambda function for processing AI transfers from the queue
  const [processAITransfersLambda, processAITransfersLogGroup] = createLambdaFunction(
    'processAITransfersHandler',
    '../dist/transfers/processAITransfers',
    'index.handler',
    {},
    processAITransfersRole
  );

  // Create event source mapping to connect the queue to the lambda
  createEventSourceMapping(
    'process-ai-transfers',
    processAITransfersLambda,
    aiTransfersQueue,
    10, // process 10 messages at a time
    30 // batch window of 30 seconds
  );

  // Create Lambda for responding to transfer offers
  const [respondToTransferOffersLambda, respondToTransferOffersLogGroup] = createLambdaFunction(
    'respondToTransferOffersHandler',
    '../dist/transfers/respondToTransferOffers',
    'index.handler'
  );

  // Create scheduled rule to trigger the respond to transfer offers lambda every hour
  createScheduledRule({
    name: 'respond-to-transfer-offers',
    description: 'Triggers a lambda to respond to transfer offers every hour',
    scheduleExpression: 'cron(0 * * * ? *)', // Every hour
    lambda: respondToTransferOffersLambda,
  });

  // Create a CloudWatch alarm for the scheduleFixtureSimulation lambda
  // This will trigger when error logs are detected
  if (stageName !== 'dev') {
    createLambdaErrorLogAlarm(
      processAuctionEndLambda,
      {
        name: 'processAuctionEndAlarm',
        description: 'Alarm for error logs in processAuctionEnd Lambda function',
        alarmActions: [errorAlarmTopic.arn],
      },
      processAuctionEndLogGroup
    );
    createLambdaErrorLogAlarm(
      processAITransfersLambda,
      {
        name: 'processAITransfersAlarm',
        description: 'Alarm for error logs in processAITransfers Lambda function',
        alarmActions: [errorAlarmTopic.arn],
      },
      processAITransfersLogGroup
    );
    createLambdaErrorLogAlarm(
      respondToTransferOffersLambda,
      {
        name: 'respondToTransferOffersAlarm',
        description: 'Alarm for error logs in respondToTransferOffers Lambda function',
        alarmActions: [errorAlarmTopic.arn],
      },
      respondToTransferOffersLogGroup
    );
  }

  return {
    submitTransferOfferLambda,
    submitBidLambda,
    processAuctionEndLambda,
    simulateAITransfersLambda,
    processAITransfersLambda,
    respondToTransferOffersLambda,
    aiTransfersQueue,
    aiTransfersDLQ,
    errorAlarmTopic,
  };
}
