# Infrastructure Configuration with Pulumi

This directory contains the Pulumi infrastructure as code (IaC) configuration for the Jumpers for Goalposts backend.

## AWS Services Configured

The infrastructure includes the following AWS services:

- API Gateway
- Lambda Functions
- DynamoDB Tables
- Cognito User Pools
- SQS Queues
- SNS Topics
- CloudWatch Alarms
- Simple Email Service (SES)

## SES Configuration

The `ses.ts` file contains the configuration for AWS Simple Email Service (SES), which is used for sending emails from the application.

### Resources Created

The SES configuration creates the following resources:

1. **Domain Identity**: Verifies domain ownership for sending emails
2. **DKIM Configuration**: Sets up DomainKeys Identified Mail for email authentication
3. **Mail From Domain**: Configures the MAIL FROM domain for bounce handling
4. **Email Identity**: Verifies an email address for sending emails
5. **IAM Policy and Role**: Grants Lambda functions permission to send emails

### Domain Verification

After deploying the infrastructure, you'll need to verify your domain with AWS SES. The Pulumi stack outputs the necessary DNS records that you need to add to your domain's DNS configuration:

1. **Domain Verification Record**: A TXT record to verify domain ownership
2. **DKIM Records**: CNAME records for DKIM verification
3. **MAIL FROM Records**: MX and TXT records for the MAIL FROM domain

### Email Verification

If you're using individual email addresses (rather than a domain), you'll need to verify each email address:

1. AWS will send a verification email to the address specified in the `emailIdentity` resource
2. Click the verification link in the email to confirm ownership

### Moving Out of the SES Sandbox

By default, new AWS accounts are placed in the SES sandbox, which limits:
- Who you can send emails to (only verified addresses/domains)
- How many emails you can send per day

To move out of the sandbox:
1. Go to the AWS SES console
2. Click on "Request Production Access"
3. Fill out the form with your use case details
4. Wait for AWS approval (usually takes 1-2 business days)

### Updating the Email Service

After configuring SES, update the email service in `src/services/email/index.ts` with:

1. The correct sender email address (must be verified in SES)
2. The correct region configuration for the SES client

Example:

```typescript
// Initialize the SES client with the correct region
const sesClient = new SESClient({ 
  region: 'us-east-2' // Make sure this matches your deployment region
});

// Update the default sender email to your verified email
export const sendEmail = async (
  recipients: string[],
  subject: string,
  content: string,
  source: string = '<EMAIL>' // Update with your verified email
): Promise<void> => {
  // ...
};
```

## Deployment

To deploy the infrastructure:

```bash
pulumi up
```

After deployment, check the stack outputs for the DNS records needed for SES verification.