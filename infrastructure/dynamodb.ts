import * as aws from '@pulumi/aws';
import { Role } from '@pulumi/aws/iam';
import { createBasicLambdaRole, stageName } from './config';

export const createDynamoDbTable = (name: string, config: aws.dynamodb.TableArgs) => {
  return new aws.dynamodb.Table(`${stageName}-${name}`, {
    ...config,
  });
};

export const addDynamoPolicyToRole = (
  name: string,
  tables: aws.dynamodb.Table[],
  allowedActions: string[],
  role?: Role
) => {
  let newRole = role || createBasicLambdaRole(name);

  tables.forEach((table, index) => {
    // Create a custom policy for DynamoDB access
    const dynamoPolicy = new aws.iam.Policy(`${stageName}-${name}DynamoPolicy${index}`, {
      policy: table.arn.apply((arn) =>
        JSON.stringify({
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Allow',
              Action: allowedActions,
              Resource: [
                arn, // Table ARN
                `${arn}/index/*`, // Index ARNs
              ],
            },
          ],
        })
      ),
    });

    // Attach the custom policy to the role
    new aws.iam.RolePolicyAttachment(`${stageName}-${name}DynamoPolicyAttachment${index}`, {
      role: newRole,
      policyArn: dynamoPolicy.arn,
    });
  });

  return newRole;
};
