import * as aws from '@pulumi/aws';
import { Role } from '@pulumi/aws/iam';
import * as pulumi from '@pulumi/pulumi';
import * as crypto from 'crypto';

export const config = new pulumi.Config();
export const logLevel = config.get('logLevel') || 'INFO';
export const serviceName = 'jfg-backend';
export const stageName = pulumi.getStack();
export const alarmEmail = config.get('alarmEmail') || '';
export const sqsBatchWindow = 0;

// Global environment variables store
const globalEnvVars: { [key: string]: pulumi.Input<string> } = {};

// Global queue permissions store
const globalQueuePermissions: Array<{
  name: string;
  queue: aws.sqs.Queue;
  permissions: 'send' | 'read' | 'both';
}> = [];

/**
 * Set a global environment variable that will be available to all Lambda functions
 * @param key Environment variable name
 * @param value Environment variable value
 */
export const setGlobalEnvVar = (key: string, value: pulumi.Input<string>): void => {
  globalEnvVars[key] = value;
};

/**
 * Get all global environment variables
 * @returns Object containing all global environment variables
 */
export const getGlobalEnvVars = (): { [key: string]: pulumi.Input<string> } => {
  return { ...globalEnvVars };
};

/**
 * Add a global queue permission that will be available to all Lambda functions
 * @param name Unique name for this permission
 * @param queue The SQS queue
 * @param permissions Type of permissions ('send', 'read', or 'both')
 */
export const addGlobalQueuePermission = (
  name: string,
  queue: aws.sqs.Queue,
  permissions: 'send' | 'read' | 'both' = 'send'
): void => {
  globalQueuePermissions.push({ name, queue, permissions });
};

/**
 * Get all global queue permissions
 * @returns Array of global queue permissions
 */
export const getGlobalQueuePermissions = () => {
  return [...globalQueuePermissions];
};

export const sha1 = (data: string): string => {
  return crypto.createHash('sha1').update(data).digest('hex');
};

export const createBasicLambdaRole = (name: string) => {
  const lambdaRole = new aws.iam.Role(`${stageName}-${name}LambdaRole`, {
    assumeRolePolicy: aws.iam.assumeRolePolicyForPrincipal({ Service: 'lambda.amazonaws.com' }),
  });

  new aws.iam.RolePolicyAttachment(`${name}LambdaRoleAttachment`, {
    role: lambdaRole,
    policyArn: aws.iam.ManagedPolicies.AWSLambdaBasicExecutionRole,
  });

  // Attach global queue permissions
  const globalPermissions = getGlobalQueuePermissions();
  globalPermissions.forEach((permission, index) => {
    const actions: string[] = [];

    if (permission.permissions === 'send' || permission.permissions === 'both') {
      actions.push('sqs:SendMessage', 'sqs:SendMessageBatch');
    }

    if (permission.permissions === 'read' || permission.permissions === 'both') {
      actions.push(
        'sqs:ReceiveMessage',
        'sqs:DeleteMessage',
        'sqs:GetQueueAttributes',
        'sqs:ChangeMessageVisibility'
      );
    }

    if (actions.length > 0) {
      const policy = new aws.iam.Policy(`${stageName}-${name}-global-${permission.name}-policy`, {
        description: `Global ${permission.permissions} policy for ${permission.name} queue`,
        policy: permission.queue.arn.apply((arn) =>
          JSON.stringify({
            Version: '2012-10-17',
            Statement: [
              {
                Effect: 'Allow',
                Action: actions,
                Resource: arn,
              },
            ],
          })
        ),
      });

      new aws.iam.RolePolicyAttachment(`${stageName}-${name}-global-${permission.name}-attachment`, {
        role: lambdaRole,
        policyArn: policy.arn,
      });
    }
  });

  return lambdaRole;
};

export const getCommonLambdaSettings = (
  name: string,
  role?: Role,
  additionalEnvVars?: { [key: string]: pulumi.Input<string> }
): aws.lambda.FunctionArgs => {
  return {
    runtime: aws.lambda.Runtime.NodeJS20dX,
    role: (role ?? createBasicLambdaRole(name)).arn,
    environment: {
      variables: {
        SERVICE_NAME: serviceName,
        LOG_LEVEL: logLevel,
        TRACER_ENABLED: 'true',
        STAGE: stageName,
        DATABASE_TYPE: config.get('databaseType') || 'dynamodb',
        DATABASE_URL: config.get('postgresUrl') || '',
        DATABASE_NAME: 'jfg',
        DATABASE_USER: config.getSecret('postgresUser') || '',
        DATABASE_PASSWORD: config.getSecret('postgresPassword') || '',
        DEBUG_USER_ID: config.get('debugUserId') || '',
        REGION: region,
        MJML_APP_ID: config.getSecret('mjmlAppId') || '',
        MJML_SECRET: config.getSecret('mjmlSecret') || '',
        // Include global environment variables first (can be overridden by function-specific variables)
        ...getGlobalEnvVars(),
        // Include function-specific environment variables (these take precedence)
        ...additionalEnvVars,
      },
    },
    memorySize: 128,
    timeout: 30,
    tracingConfig: {
      mode: 'Active',
    },
  };
};
export const region = 'us-east-2';
