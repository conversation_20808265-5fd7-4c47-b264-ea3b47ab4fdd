#!/bin/bash
set -e

echo "Setting up development environment for jumpers-for-goalposts-backend..."

# Install npm dependencies
echo "Installing npm dependencies..."
npm install

# Create temp directory for MikroORM metadata cache
mkdir -p temp

# Create .env file for local development if it doesn't exist
if [ ! -f .env ]; then
    echo "Creating .env file for local development..."
    cat > .env << EOL
# Environment
NODE_ENV=development
STAGE=dev
LOG_LEVEL=DEBUG

# Database Configuration
DATABASE_TYPE=dynamodb
DATABASE_URL=localhost
DATABASE_NAME=jfg
DATABASE_USER=postgres
DATABASE_PASSWORD=postgres

# AWS Configuration for LocalStack
AWS_REGION=us-east-2
AWS_DEFAULT_REGION=us-east-2
AWS_ACCESS_KEY_ID=local
AWS_SECRET_ACCESS_KEY=local
DYNAMODB_PREFIX=jfg-
REGION=us-east-2

# Service Configuration
SERVICE_NAME=jfg-backend

# DynamoDB Table Names
LEAGUES_TABLE_NAME=leaguesTable
TEAMS_TABLE_NAME=teamsTable
FIXTURES_TABLE_NAME=fixturesTable
PLAYERS_TABLE_NAME=playersTable
MANAGERS_TABLE_NAME=managersTable
TRANSFER_LISTED_PLAYERS_TABLE_NAME=transfer-listed-players-table
AVAILABLE_TEAMS_TABLE_NAME=availableTeamsTable
SCOUTING_REQUESTS_TABLE_NAME=scoutingRequestsTable
SCOUTED_PLAYERS_TABLE_NAME=scoutedPlayersTable
EOL
    echo ".env file created."
fi

# Check if Docker is installed
if command -v docker &> /dev/null; then
    echo "Docker is installed. You can start LocalStack with:"
    echo "  npm run localstack:up"
else
    echo "Docker is not installed. For local development with LocalStack, consider installing Docker."
    echo "You can install Docker by following the instructions at: https://docs.docker.com/get-docker/"
fi

# Check if PostgreSQL is installed
if command -v psql &> /dev/null; then
    echo "PostgreSQL is installed."
    echo "Note: Make sure PostgreSQL is running and accessible with the credentials in .env"
else
    echo "PostgreSQL is not installed. If you want to use PostgreSQL instead of DynamoDB, consider installing PostgreSQL."
    echo "You can install PostgreSQL with: sudo apt-get install -y postgresql postgresql-contrib"
fi

# Build the project
echo "Building the project..."
npm run build

echo "Setup completed successfully!"
echo "You can now run the following commands:"
echo "  - npm run localstack:up    # Start LocalStack for local development"
echo "  - npm run offline          # Start the Serverless offline server"
echo "  - npm run build            # Build the project"
echo "  - npm run test             # Run tests"